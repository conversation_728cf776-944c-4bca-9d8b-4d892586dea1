/**
 * Safari-specific fixes and utilities
 * Addresses various Safari quirks with SPAs
 */

export const isSafari = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('safari') && !userAgent.includes('chrome') && !userAgent.includes('chromium');
};

export const isMobileSafari = (): boolean => {
  const userAgent = navigator.userAgent.toLowerCase();
  return userAgent.includes('safari') && userAgent.includes('mobile');
};

/**
 * Apply Safari-specific fixes on app initialization
 */
export const applySafariFixes = () => {
  if (!isSafari()) return;

  console.log('Safari detected, applying compatibility fixes...');

  // Fix 1: Prevent Safari from caching pages aggressively
  window.addEventListener('pageshow', (event) => {
    if (event.persisted) {
      console.log('Safari page cache detected, forcing reload');
      window.location.reload();
    }
  });

  // Fix 2: <PERSON>le <PERSON>'s back/forward button behavior
  window.addEventListener('popstate', (event) => {
    console.log('Safari popstate event detected', event.state);
    // Force a small delay to let wouter process the change
    setTimeout(() => {
      if (window.location.pathname !== window.history.state?.path) {
        console.log('Safari navigation mismatch, forcing sync');
        window.dispatchEvent(new PopStateEvent('popstate', { state: event.state }));
      }
    }, 10);
  });

  // Fix 3: Prevent Safari from blocking programmatic navigation
  const originalPushState = window.history.pushState;
  const originalReplaceState = window.history.replaceState;

  window.history.pushState = function(state, title, url) {
    console.log('Safari pushState intercepted:', url);
    originalPushState.call(this, state, title, url);
    
    // Dispatch a custom event to notify wouter
    window.dispatchEvent(new CustomEvent('safariNavigation', { 
      detail: { url, state, type: 'push' } 
    }));
  };

  window.history.replaceState = function(state, title, url) {
    console.log('Safari replaceState intercepted:', url);
    originalReplaceState.call(this, state, title, url);
    
    // Dispatch a custom event to notify wouter
    window.dispatchEvent(new CustomEvent('safariNavigation', { 
      detail: { url, state, type: 'replace' } 
    }));
  };

  // Fix 4: Handle Safari's viewport issues
  const handleViewportChange = () => {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport && isMobileSafari()) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }
  };

  handleViewportChange();
  window.addEventListener('orientationchange', handleViewportChange);

  // Fix 5: Safari layout and viewport optimizations
  applySafariLayoutFixes();

  console.log('Safari fixes applied successfully');
};

/**
 * Apply Safari-specific layout and viewport optimizations
 */
export const applySafariLayoutFixes = () => {
  if (!isSafari()) return;

  console.log('Applying Safari layout optimizations...');

  // Add Safari-specific CSS variables and styles
  const safariStyles = document.createElement('style');
  safariStyles.id = 'safari-layout-fixes';
  safariStyles.textContent = `
    /* Safari-specific layout optimizations */
    @supports (-webkit-appearance: none) {
      :root {
        --safari-vh: 100vh;
        --safari-vw: 100vw;
      }

      /* Fix Safari's 100vh issue */
      .h-screen {
        height: 100vh !important;
        height: -webkit-fill-available !important;
        min-height: -webkit-fill-available !important;
      }

      /* Ensure flex containers work properly in Safari */
      .flex {
        display: -webkit-box !important;
        display: -webkit-flex !important;
        display: flex !important;
      }

      .flex-col {
        -webkit-box-orient: vertical !important;
        -webkit-box-direction: normal !important;
        -webkit-flex-direction: column !important;
        flex-direction: column !important;
      }

      .flex-1 {
        -webkit-box-flex: 1 !important;
        -webkit-flex: 1 1 0% !important;
        flex: 1 1 0% !important;
      }

      /* Fix Safari overflow issues */
      .overflow-hidden {
        overflow: hidden !important;
        -webkit-overflow-scrolling: touch !important;
      }

      .overflow-y-auto {
        overflow-y: auto !important;
        -webkit-overflow-scrolling: touch !important;
      }

      /* Safari grid fixes */
      .grid {
        display: -ms-grid !important;
        display: grid !important;
      }

      /* Safari backdrop-filter support */
      .backdrop-blur-sm {
        -webkit-backdrop-filter: blur(4px) !important;
        backdrop-filter: blur(4px) !important;
      }

      /* Safari transform optimizations */
      .transform {
        -webkit-transform: translateZ(0) !important;
        transform: translateZ(0) !important;
      }

      /* Force hardware acceleration for better performance */
      .motion-safe\\:animate-spin,
      .animate-spin {
        -webkit-transform: translateZ(0) !important;
        transform: translateZ(0) !important;
        will-change: transform !important;
      }

      /* Safari-specific scrollbar styling */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      ::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 3px;
      }

      ::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: rgba(0, 0, 0, 0.5);
      }
    }
  `;

  // Remove existing Safari styles if they exist
  const existingStyles = document.getElementById('safari-layout-fixes');
  if (existingStyles) {
    existingStyles.remove();
  }

  // Add the new styles
  document.head.appendChild(safariStyles);

  // Handle Safari's viewport height issues
  const updateSafariViewport = () => {
    if (isMobileSafari()) {
      const vh = window.innerHeight * 0.01;
      document.documentElement.style.setProperty('--safari-vh', `${vh}px`);

      const vw = window.innerWidth * 0.01;
      document.documentElement.style.setProperty('--safari-vw', `${vw}px`);
    }
  };

  updateSafariViewport();
  window.addEventListener('resize', updateSafariViewport);
  window.addEventListener('orientationchange', () => {
    setTimeout(updateSafariViewport, 100);
  });

  console.log('Safari layout optimizations applied');
};

/**
 * Force navigation in Safari using multiple methods
 */
export const safariForceNavigate = (path: string): void => {
  console.log(`Safari force navigate to: ${path}`);
  
  try {
    // Method 1: Try history API
    window.history.pushState({}, '', path);
    window.dispatchEvent(new PopStateEvent('popstate'));
    
    // Method 2: Fallback to location change
    setTimeout(() => {
      if (window.location.pathname !== path) {
        console.log('History API failed, using location.href');
        window.location.href = path;
      }
    }, 100);
    
  } catch (error) {
    console.error('Safari navigation error:', error);
    window.location.href = path;
  }
};

/**
 * Create a Safari-compatible link handler
 */
export const createSafariLinkHandler = (path: string) => {
  return (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (isSafari()) {
      safariForceNavigate(path);
    } else {
      window.location.href = path;
    }
  };
};
