/**
 * Editor de Imágenes con IA - Página principal
 * Incluye 7 herramientas de edición usando Stability AI:
 * 1. <PERSON><PERSON><PERSON>, 2. <PERSON><PERSON><PERSON>, 3. <PERSON><PERSON><PERSON>,
 * 4. <PERSON><PERSON><PERSON><PERSON><PERSON>, 5. <PERSON><PERSON><PERSON>, 6. <PERSON><PERSON><PERSON> Fondo, 7. <PERSON><PERSON><PERSON><PERSON><PERSON>
 */
import React, { useState, useRef, useEffect, useCallback } from "react";
import { Link } from "wouter";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowLeft,
  Upload,
  Wand2,
  Eraser,
  PaintBucket,
  Expand,
  RefreshCw,
  Palette,
  Scissors,
  Image as ImageIcon,
  Sparkles,
  Crown,
  Zap,
  Cpu,
  Info,
  Settings,
  RotateCcw,
  Heart,
  Trash2,
  Copy,
  Download,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { replaceBackgroundWithPolling, ReplaceBackgroundOptions } from "@/services/stability-replace-background-service";
import { upscaleImageWithPolling, UpscaleOptions, getUpscaleModes, UpscaleModeInfo, StylePresetInfo } from "@/services/stability-upscale-service";
import UpscaleImagePage from "@/pages/upscale-image-page";

// Tipos de herramientas disponibles
type AIEditTool =
  | "erase-objects"
  | "inpaint-areas"
  | "outpaint-image"
  | "search-replace"
  | "search-recolor"
  | "remove-background"
  | "replace-background"
  | "upscale-image";

// Interfaz para imágenes editadas guardadas
interface SavedEdit {
  id: string;
  originalUrl: string;
  processedUrl: string;
  originalFilename: string;
  tool: AIEditTool;
  toolName: string;
  parameters: Record<string, any>;
  timestamp: number;
  isFavorite: boolean;
}

// Custom hook para localStorage que funciona mejor en Safari
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);

      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Funciones para manejar imágenes editadas guardadas
const SAVED_EDITS_KEY = 'emma_saved_ai_image_edits';

const createSavedEdit = (editData: Omit<SavedEdit, 'id' | 'timestamp' | 'isFavorite'>): SavedEdit => {
  const timestamp = Date.now();
  const randomPart = Math.floor(Math.random() * 1000000).toString();

  return {
    ...editData,
    id: `edit_${timestamp}_${randomPart}`,
    timestamp: timestamp,
    isFavorite: true,
  };
};

const isEditSaved = (imageUrl: string, savedEdits: SavedEdit[]): boolean => {
  return savedEdits.some(edit => edit.processedUrl === imageUrl);
};

// Información de cada herramienta
interface ToolInfo {
  id: AIEditTool;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  credits: number;
  complexity: "simple" | "medium" | "advanced";
  requiresMask: boolean;
  requiresPrompt: boolean;
}

// Configuración de las 7 herramientas de IA
const AI_EDIT_TOOLS: ToolInfo[] = [
  {
    id: "erase-objects",
    name: "Borrar Objetos",
    description: "Elimina objetos no deseados de la imagen usando máscaras",
    icon: <Eraser size={24} />,
    color: "text-red-500",
    credits: 3,
    complexity: "medium",
    requiresMask: true,
    requiresPrompt: false,
  },
  {
    id: "inpaint-areas",
    name: "Rellenar Áreas",
    description: "Rellena áreas específicas con contenido nuevo basado en prompts",
    icon: <PaintBucket size={24} />,
    color: "text-blue-500",
    credits: 3,
    complexity: "advanced",
    requiresMask: true,
    requiresPrompt: true,
  },
  {
    id: "outpaint-image",
    name: "Expandir Imagen",
    description: "Extiende la imagen más allá de sus bordes originales",
    icon: <Expand size={24} />,
    color: "text-green-500",
    credits: 4,
    complexity: "advanced",
    requiresMask: false,
    requiresPrompt: true,
  },
  {
    id: "search-replace",
    name: "Reemplazar Objetos",
    description: "Busca y reemplaza objetos específicos en la imagen",
    icon: <RefreshCw size={24} />,
    color: "text-purple-500",
    credits: 3,
    complexity: "advanced",
    requiresMask: false,
    requiresPrompt: true,
  },
  {
    id: "search-recolor",
    name: "Cambiar Colores",
    description: "Cambia los colores de objetos específicos manteniendo texturas",
    icon: <Palette size={24} />,
    color: "text-orange-500",
    credits: 3,
    complexity: "medium",
    requiresMask: false,
    requiresPrompt: true,
  },
  {
    id: "remove-background",
    name: "Eliminar Fondo",
    description: "Elimina automáticamente el fondo de la imagen",
    icon: <Scissors size={24} />,
    color: "text-indigo-500",
    credits: 3,
    complexity: "simple",
    requiresMask: false,
    requiresPrompt: false,
  },
  {
    id: "replace-background",
    name: "Reemplazar Fondo",
    description: "Reemplaza el fondo con nuevos escenarios e iluminación",
    icon: <ImageIcon size={24} />,
    color: "text-teal-500",
    credits: 8,
    complexity: "advanced",
    requiresMask: false,
    requiresPrompt: true,
  },
  {
    id: "upscale-image",
    name: "Mejorar Calidad",
    description: "Mejora la resolución y calidad de la imagen hasta 4K",
    icon: <Sparkles size={24} />,
    color: "text-yellow-500",
    credits: 1,
    complexity: "simple",
    requiresMask: false,
    requiresPrompt: false,
  },
];



export default function AIImageEditor() {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Estados principales
  const [selectedTool, setSelectedTool] = useState<AIEditTool | null>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedImageFile, setUploadedImageFile] = useState<File | null>(null);
  const [originalFilename, setOriginalFilename] = useState<string>("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [resultImage, setResultImage] = useState<string | null>(null);

  // Sistema de guardados
  const [savedEdits, setSavedEdits] = useLocalStorage<SavedEdit[]>(SAVED_EDITS_KEY, []);
  const [currentEditSaved, setCurrentEditSaved] = useState(false);
  const [activeTab, setActiveTab] = useState("latest");

  // Manejo de carga de archivos
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleFileUpload = (file: File) => {
    // Validar tipo de archivo
    if (!file.type.startsWith('image/')) {
      toast({
        title: "❌ Archivo inválido",
        description: "Por favor selecciona una imagen válida (JPG, PNG, WebP)",
        variant: "destructive",
      });
      return;
    }

    // Validar tamaño (máximo 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "❌ Archivo muy grande",
        description: "El archivo debe ser menor a 10MB",
        variant: "destructive",
      });
      return;
    }

    setUploadedImageFile(file);
    setOriginalFilename(file.name);
    setResultImage(null);
    setCurrentEditSaved(false);

    // Crear URL para preview
    const reader = new FileReader();
    reader.onload = () => {
      setUploadedImage(reader.result as string);
    };
    reader.readAsDataURL(file);

    toast({
      title: "✅ Imagen cargada",
      description: `${file.name} está lista para editar`,
    });
  };

  // Drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  // Seleccionar herramienta
  const handleToolSelect = (tool: AIEditTool) => {
    setSelectedTool(tool);
    setResultImage(null);
    setCurrentEditSaved(false);

    const toolInfo = AI_EDIT_TOOLS.find(t => t.id === tool);
    toast({
      title: `🛠️ ${toolInfo?.name} seleccionada`,
      description: toolInfo?.description,
    });
  };

  // Resetear todo
  const resetAll = () => {
    setSelectedTool(null);
    setUploadedImage(null);
    setUploadedImageFile(null);
    setOriginalFilename("");
    setResultImage(null);
    setCurrentEditSaved(false);
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!resultImage || !uploadedImage || !selectedTool) return;

    try {
      if (currentEditSaved) {
        // Quitar de favoritos
        const savedEdit = savedEdits.find(edit => edit.processedUrl === resultImage);
        if (savedEdit) {
          const filteredEdits = savedEdits.filter(edit => edit.id !== savedEdit.id);
          setSavedEdits(filteredEdits);
          setCurrentEditSaved(false);

          toast({
            title: "💔 Eliminada de favoritos",
            description: "La imagen ha sido eliminada de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const toolInfo = AI_EDIT_TOOLS.find(t => t.id === selectedTool);

        const editData = {
          originalUrl: uploadedImage,
          processedUrl: resultImage,
          originalFilename: originalFilename || "imagen",
          tool: selectedTool,
          toolName: toolInfo?.name || "Herramienta",
          parameters: {}, // Se puede expandir para incluir parámetros específicos
        };

        const newEdit = createSavedEdit(editData);
        const updatedEdits = [newEdit, ...savedEdits].slice(0, 50); // Limitar a 50

        setSavedEdits(updatedEdits);
        setCurrentEditSaved(true);

        toast({
          title: "❤️ ¡Guardada en favoritos!",
          description: `Imagen editada con ${toolInfo?.name} guardada exitosamente.`,
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [resultImage, uploadedImage, selectedTool, originalFilename, currentEditSaved, savedEdits, setSavedEdits, toast]);

  // Verificar si la imagen actual está guardada
  useEffect(() => {
    if (resultImage) {
      setCurrentEditSaved(isEditSaved(resultImage, savedEdits));
    }
  }, [resultImage, savedEdits]);

  return (
    <DashboardLayout pageTitle="Editor de Imágenes con IA">
      <TooltipProvider>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                  <h1 className="text-4xl font-bold text-white">
                    Editor de Imágenes con IA
                  </h1>
                </div>
                <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                  8 herramientas profesionales de edición con IA avanzada. Edita, mejora y transforma tus imágenes.
                </p>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Crown className="w-3 h-3 mr-1" />
                    Calidad profesional
                  </Badge>
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Zap className="w-3 h-3 mr-1" />
                    3 créditos por uso
                  </Badge>
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Settings className="w-3 h-3 mr-1" />
                    8 herramientas
                  </Badge>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Step-by-step workflow */}
          <div className="max-w-7xl mx-auto space-y-8">

            {/* Step 1: Tool Selection */}
            {!selectedTool && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="text-center pb-6">
                    <CardTitle className="text-2xl font-bold text-gray-800">
                      Paso 1: Selecciona tu Herramienta de Edición
                    </CardTitle>
                    <CardDescription className="text-lg text-gray-600">
                      Elige la herramienta perfecta para transformar tu imagen
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {AI_EDIT_TOOLS.map((tool, index) => (
                        <motion.div
                          key={tool.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.1 }}
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Card
                            className="cursor-pointer hover:shadow-lg transition-all duration-300 border-2 hover:border-purple-300 bg-gradient-to-br from-white to-gray-50"
                            onClick={() => handleToolSelect(tool.id)}
                          >
                            <CardContent className="p-6 text-center">
                              <div className={`${tool.color} mx-auto mb-4 p-4 rounded-2xl bg-gray-100 w-fit`}>
                                {tool.icon}
                              </div>
                              <h3 className="font-bold text-lg mb-2">{tool.name}</h3>
                              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                {tool.description}
                              </p>
                              <div className="flex items-center justify-center gap-2">
                                <Badge variant="outline" className="text-xs">
                                  {tool.credits} créditos
                                </Badge>
                                <Badge
                                  variant="secondary"
                                  className={`text-xs ${
                                    tool.complexity === 'simple' ? 'bg-green-100 text-green-800' :
                                    tool.complexity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                    'bg-red-100 text-red-800'
                                  }`}
                                >
                                  {tool.complexity === 'simple' ? 'Fácil' :
                                   tool.complexity === 'medium' ? 'Medio' : 'Avanzado'}
                                </Badge>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Step 2: Image Upload & Tool Configuration */}
            {selectedTool && (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

                {/* Left Panel: Image Upload */}
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.2 }}
                  className="lg:col-span-1"
                >
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-xl">
                        <ImageIcon className="h-5 w-5 text-purple-600" />
                        Paso 2: Sube tu Imagen
                      </CardTitle>
                      <CardDescription>
                        Carga la imagen que quieres editar
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {!uploadedImage ? (
                        <div className="space-y-6">
                          {/* Upload Area */}
                          <div
                            className="border-2 border-dashed border-purple-300 rounded-xl p-8 text-center hover:border-purple-400 transition-colors cursor-pointer bg-gradient-to-br from-purple-50 to-blue-50"
                            onDragOver={handleDragOver}
                            onDrop={handleDrop}
                            onClick={() => fileInputRef.current?.click()}
                          >
                            <Upload className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold mb-2 text-gray-800">
                              Arrastra una imagen aquí
                            </h3>
                            <p className="text-gray-600 mb-4">
                              o haz clic para seleccionar
                            </p>
                            <p className="text-sm text-gray-500 mb-4">
                              JPG, PNG, WebP (máximo 10MB)
                            </p>
                            <Button className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
                              <Upload className="w-4 h-4 mr-2" />
                              Seleccionar Imagen
                            </Button>
                          </div>
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept="image/*"
                            onChange={handleFileChange}
                            className="hidden"
                          />

                          {/* Saved Images Preview */}
                          {savedEdits.length > 0 && (
                            <div className="space-y-4">
                              <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                                <Heart className="h-4 w-4 text-purple-500" />
                                <span>O usa una imagen guardada ({savedEdits.length})</span>
                              </div>
                              <div className="grid grid-cols-2 gap-3">
                                {savedEdits.slice(0, 4).map((savedEdit) => {
                                  const toolInfo = AI_EDIT_TOOLS.find(t => t.id === savedEdit.tool);
                                  return (
                                    <motion.div
                                      key={savedEdit.id}
                                      whileHover={{ scale: 1.05 }}
                                      className="relative group cursor-pointer"
                                      onClick={() => {
                                        setUploadedImage(savedEdit.processedUrl);
                                        setOriginalFilename(savedEdit.originalFilename);
                                        setUploadedImageFile(null);
                                        setResultImage(null);
                                        toast({
                                          title: "📸 Imagen cargada",
                                          description: `${savedEdit.originalFilename} lista para editar`,
                                        });
                                      }}
                                    >
                                      <div className="relative aspect-square border-2 border-gray-200 rounded-lg overflow-hidden hover:border-purple-300 transition-colors">
                                        <img
                                          src={savedEdit.processedUrl}
                                          alt="Imagen guardada"
                                          className="w-full h-full object-cover"
                                        />
                                        <div className="absolute top-1 right-1">
                                          <Badge className="bg-white/90 text-xs text-purple-600">
                                            {toolInfo?.name || savedEdit.toolName}
                                          </Badge>
                                        </div>
                                      </div>
                                      <p className="text-xs text-gray-600 truncate mt-1">
                                        {savedEdit.originalFilename}
                                      </p>
                                    </motion.div>
                                  );
                                })}
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        /* Image Preview */
                        <div className="space-y-4">
                          <div className="relative border-2 border-gray-200 rounded-xl overflow-hidden">
                            <img
                              src={uploadedImage}
                              alt="Imagen original"
                              className="w-full h-auto max-h-80 object-contain"
                            />
                            <div className="absolute top-2 right-2">
                              <Badge className="bg-blue-500 text-white">
                                Original
                              </Badge>
                            </div>
                          </div>
                          <div className="text-sm text-gray-600 space-y-1">
                            <p><strong>Archivo:</strong> {originalFilename}</p>
                            <p><strong>Tamaño:</strong> {uploadedImageFile ? Math.round(uploadedImageFile.size / 1024) : 0} KB</p>
                          </div>
                          <Button
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                            className="w-full"
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            Cambiar Imagen
                          </Button>
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept="image/*"
                            onChange={handleFileChange}
                            className="hidden"
                          />
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Right Panel: Tool Configuration */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                  className="lg:col-span-2"
                >
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {(() => {
                            const tool = AI_EDIT_TOOLS.find(t => t.id === selectedTool);
                            return tool ? (
                              <>
                                <div className={`${tool.color} p-3 rounded-xl bg-gray-100`}>
                                  {tool.icon}
                                </div>
                                <div>
                                  <CardTitle className="text-xl">
                                    Paso 3: Configura {tool.name}
                                  </CardTitle>
                                  <CardDescription className="text-base">
                                    {tool.description}
                                  </CardDescription>
                                </div>
                              </>
                            ) : null;
                          })()}
                        </div>
                        <Button variant="outline" onClick={resetAll} size="sm">
                          <ArrowLeft className="w-4 h-4 mr-2" />
                          Cambiar Herramienta
                        </Button>
                      </div>
                    </CardHeader>
                    <CardContent>
                      {!uploadedImage ? (
                        <div className="text-center py-12">
                          <div className="text-6xl mb-4">📸</div>
                          <h3 className="text-xl font-semibold mb-3 text-gray-800">
                            Sube una imagen para comenzar
                          </h3>
                          <p className="text-gray-600 mb-6">
                            Carga tu imagen en el panel izquierdo para usar <strong>{AI_EDIT_TOOLS.find(t => t.id === selectedTool)?.name}</strong>
                          </p>
                          <Button
                            onClick={() => fileInputRef.current?.click()}
                            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                          >
                            <Upload className="w-4 h-4 mr-2" />
                            Seleccionar Imagen
                          </Button>
                        </div>
                      ) : (
                        /* Tool-specific interfaces */
                        <div className="space-y-6">
                          {selectedTool === "remove-background" && (
                            <RemoveBackgroundTool
                              imageFile={uploadedImageFile!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {selectedTool === "erase-objects" && (
                            <EraseObjectsTool
                              imageFile={uploadedImageFile!}
                              imageUrl={uploadedImage!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {selectedTool === "inpaint-areas" && (
                            <InpaintAreasTool
                              imageFile={uploadedImageFile!}
                              imageUrl={uploadedImage!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {selectedTool === "outpaint-image" && (
                            <OutpaintImageTool
                              imageFile={uploadedImageFile!}
                              imageUrl={uploadedImage!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {selectedTool === "search-replace" && (
                            <SearchReplaceTool
                              imageFile={uploadedImageFile!}
                              imageUrl={uploadedImage!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {selectedTool === "search-recolor" && (
                            <SearchRecolorTool
                              imageFile={uploadedImageFile!}
                              imageUrl={uploadedImage!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {selectedTool === "replace-background" && (
                            <ReplaceBackgroundTool
                              imageFile={uploadedImageFile!}
                              imageUrl={uploadedImage!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {selectedTool === "upscale-image" && (
                            <UpscaleImageTool
                              imageFile={uploadedImageFile!}
                              onResult={setResultImage}
                              isProcessing={isProcessing}
                              setIsProcessing={setIsProcessing}
                            />
                          )}

                          {/* Placeholder for unimplemented tools */}
                          {!["remove-background", "erase-objects", "inpaint-areas", "outpaint-image", "search-replace", "search-recolor", "replace-background", "upscale-image"].includes(selectedTool) && (
                            <div className="text-center py-12">
                              <div className="text-6xl mb-4">🚧</div>
                              <h3 className="text-xl font-semibold mb-3 text-gray-800">Próximamente</h3>
                              <p className="text-gray-600 mb-6">
                                La interfaz para <strong>{AI_EDIT_TOOLS.find(t => t.id === selectedTool)?.name}</strong> estará disponible pronto.
                              </p>
                              <Button disabled className="bg-gray-400">
                                <Wand2 className="w-4 h-4 mr-2" />
                                Procesar Imagen
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </CardContent>
              </Card>
            </motion.div>

            {/* Step 4: Results */}
            {resultImage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                data-results-section
              >
                <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                  <CardHeader className="text-center pb-6">
                    <CardTitle className="text-2xl font-bold text-gray-800 flex items-center justify-center gap-2">
                      <Sparkles className="h-6 w-6 text-purple-600" />
                      Paso 4: Tu Imagen Editada
                    </CardTitle>
                    <CardDescription className="text-lg text-gray-600">
                      ¡Perfecto! Tu imagen ha sido procesada exitosamente
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                      <TabsList className="grid w-full grid-cols-2 mb-6">
                        <TabsTrigger value="latest" className="text-sm">
                          <Sparkles className="w-4 h-4 mr-2" />
                          Resultado Actual
                        </TabsTrigger>
                        <TabsTrigger value="saved" className="text-sm">
                          <Heart className="w-4 h-4 mr-2" />
                          Guardados ({savedEdits.length})
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="latest" className="mt-6">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                          {/* Original Image */}
                          <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                              <ImageIcon className="w-5 h-5" />
                              Imagen Original
                            </h3>
                            <div className="relative border-2 border-gray-200 rounded-xl overflow-hidden">
                              <img
                                src={uploadedImage}
                                alt="Imagen original"
                                className="w-full h-auto max-h-80 object-contain"
                              />
                              <div className="absolute top-2 right-2">
                                <Badge className="bg-blue-500 text-white">
                                  Original
                                </Badge>
                              </div>
                            </div>
                          </div>

                          {/* Processed Image */}
                          <div className="space-y-4">
                            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                              <Sparkles className="w-5 h-5 text-purple-600" />
                              Imagen Procesada
                            </h3>
                            <div className="relative border-2 border-purple-200 rounded-xl overflow-hidden">
                              <img
                                src={resultImage}
                                alt="Imagen procesada"
                                className="w-full h-auto max-h-80 object-contain"
                              />
                              <div className="absolute top-2 right-2">
                                <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                                  ✨ Editada
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="flex flex-col sm:flex-row gap-4 mt-6">
                          <Button
                            onClick={() => {
                              const link = document.createElement('a');
                              link.href = resultImage;
                              link.download = `edited-${originalFilename}`;
                              link.click();
                              toast({
                                title: "📥 Descarga iniciada",
                                description: "Tu imagen editada se está descargando.",
                              });
                            }}
                            className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                          >
                            <Download className="w-4 h-4 mr-2" />
                            Descargar Imagen
                          </Button>
                          <Button
                            variant={currentEditSaved ? "destructive" : "outline"}
                            onClick={handleToggleFavorite}
                            className="flex-1"
                          >
                            {currentEditSaved ? (
                              <>
                                <Heart className="w-4 h-4 mr-2 fill-current" />
                                Guardado en Favoritos
                              </>
                            ) : (
                              <>
                                <Heart className="w-4 h-4 mr-2" />
                                Guardar en Favoritos
                              </>
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => {
                              setUploadedImage(resultImage);
                              setOriginalFilename(`edited-${originalFilename}`);
                              setUploadedImageFile(null);
                              setResultImage(null);
                              setCurrentEditSaved(false);
                              toast({
                                title: "🔄 Imagen cargada",
                                description: "Ahora puedes aplicar otra edición a esta imagen",
                              });
                            }}
                            className="flex-1"
                          >
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Editar Nuevamente
                          </Button>
                        </div>
                      </TabsContent>
                      <TabsContent value="saved" className="mt-6">
                        {savedEdits.length === 0 ? (
                          <div className="text-center py-12">
                            <div className="text-6xl mb-4">📁</div>
                            <h3 className="text-xl font-semibold mb-3 text-gray-800">Sin imágenes guardadas</h3>
                            <p className="text-gray-600">
                              Las imágenes que guardes aparecerán aquí para fácil acceso
                            </p>
                          </div>
                        ) : (
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            {savedEdits.map((savedEdit) => {
                              const toolInfo = AI_EDIT_TOOLS.find(t => t.id === savedEdit.tool);
                              return (
                                <motion.div
                                  key={savedEdit.id}
                                  initial={{ opacity: 0, scale: 0.9 }}
                                  animate={{ opacity: 1, scale: 1 }}
                                  className="bg-white border-2 border-gray-200 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:border-purple-300"
                                >
                                  {/* Image */}
                                  <div className="relative aspect-square">
                                    <img
                                      src={savedEdit.processedUrl}
                                      alt="Imagen editada guardada"
                                      className="w-full h-full object-cover"
                                    />
                                    <div className="absolute top-2 left-2">
                                      <Badge className="bg-white/90 text-purple-600 text-xs font-medium">
                                        {toolInfo?.name || savedEdit.toolName}
                                      </Badge>
                                    </div>
                                  </div>

                                  {/* Info */}
                                  <div className="p-4">
                                    <div className="flex items-center justify-between mb-3">
                                      <h4 className="font-semibold text-sm truncate text-gray-800">
                                        {savedEdit.originalFilename}
                                      </h4>
                                      <span className="text-xs text-gray-500">
                                        {new Date(savedEdit.timestamp).toLocaleDateString()}
                                      </span>
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="flex gap-2 mb-2">
                                      <Button
                                        size="sm"
                                        variant="default"
                                        onClick={() => {
                                          setUploadedImage(savedEdit.processedUrl);
                                          setOriginalFilename(savedEdit.originalFilename);
                                          setUploadedImageFile(null);
                                          setResultImage(null);
                                          toast({
                                            title: "📸 Imagen cargada",
                                            description: `${savedEdit.originalFilename} lista para editar`,
                                          });
                                        }}
                                        className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                                      >
                                        <Upload className="h-3 w-3 mr-1" />
                                        Cargar
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          const link = document.createElement('a');
                                          link.href = savedEdit.processedUrl;
                                          link.download = `edited-${savedEdit.originalFilename}`;
                                          link.click();
                                          toast({
                                            title: "📥 Descarga iniciada",
                                            description: "Tu imagen se está descargando.",
                                          });
                                        }}
                                        className="flex-1"
                                      >
                                        <Download className="h-3 w-3 mr-1" />
                                        Descargar
                                      </Button>
                                    </div>
                                    <div className="flex gap-2">
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          navigator.clipboard.writeText(savedEdit.processedUrl);
                                          toast({
                                            title: "📋 Copiado",
                                            description: "URL de la imagen copiada al portapapeles.",
                                          });
                                        }}
                                        className="flex-1"
                                      >
                                        <Copy className="h-3 w-3 mr-1" />
                                        Copiar
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="destructive"
                                        onClick={() => {
                                          const filteredEdits = savedEdits.filter(edit => edit.id !== savedEdit.id);
                                          setSavedEdits(filteredEdits);
                                          toast({
                                            title: "💔 Eliminada",
                                            description: "Imagen eliminada de favoritos.",
                                          });
                                        }}
                                        className="flex-1"
                                      >
                                        <Trash2 className="h-3 w-3 mr-1" />
                                        Eliminar
                                      </Button>
                                    </div>
                                  </div>
                                </motion.div>
                              );
                            })}
                          </div>
                        )}
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </TooltipProvider>
    </DashboardLayout>
  );
}

// Componente específico para eliminar fondo
interface RemoveBackgroundToolProps {
  imageFile: File;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function RemoveBackgroundTool({ imageFile, onResult, isProcessing, setIsProcessing }: RemoveBackgroundToolProps) {
  const { toast } = useToast();
  const [outputFormat, setOutputFormat] = useState<"png" | "webp">("png");

  const handleRemoveBackground = async () => {
    if (!imageFile) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para procesar",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('output_format', outputFormat);

      // Llamar a la API
      const response = await fetch('/api/v1/images/remove-background', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success && result.url) {
        onResult(result.url);
        toast({
          title: "🎉 ¡Fondo eliminado!",
          description: "La imagen ha sido procesada exitosamente",
        });
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error removing background:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-900">Eliminar Fondo</h4>
            <p className="text-sm text-blue-700 mt-1">
              Esta herramienta elimina automáticamente el fondo de la imagen, manteniendo solo el sujeto principal.
              Ideal para productos, retratos y objetos.
            </p>
          </div>
        </div>
      </div>

      {/* Configuración */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Formato de salida</label>
          <div className="grid grid-cols-2 gap-2">
            {(['png', 'webp'] as const).map((format) => (
              <Button
                key={format}
                variant={outputFormat === format ? "default" : "outline"}
                onClick={() => setOutputFormat(format)}
                className="text-sm"
                disabled={isProcessing}
              >
                {format.toUpperCase()}
              </Button>
            ))}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            PNG mantiene transparencia perfecta, WebP es más eficiente en tamaño
          </p>
        </div>
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleRemoveBackground}
        disabled={isProcessing}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Eliminando fondo...
          </>
        ) : (
          <>
            <Scissors className="w-4 h-4 mr-2" />
            Eliminar Fondo
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>2 créditos</strong></p>
      </div>
    </div>
  );
}

// Componente específico para borrar objetos
interface EraseObjectsToolProps {
  imageFile: File;
  imageUrl: string;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function EraseObjectsTool({ imageFile, imageUrl, onResult, isProcessing, setIsProcessing }: EraseObjectsToolProps) {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("webp");
  const [growMask, setGrowMask] = useState(5);
  const [brushSize, setBrushSize] = useState(20);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasMask, setHasMask] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  // Cargar la imagen en el canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageUrl) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = imageUrl;

    img.onload = () => {
      // Ajustar el tamaño del canvas
      canvas.width = img.width;
      canvas.height = img.height;

      // Dibujar la imagen original
      ctx.drawImage(img, 0, 0);

      // Configurar el contexto para dibujar la máscara
      ctx.lineJoin = "round";
      ctx.lineCap = "round";
      ctx.strokeStyle = "rgba(255, 255, 255, 0.8)"; // Blanco semi-transparente
      ctx.lineWidth = brushSize;

      setImageLoaded(true);
    };
  }, [imageUrl]);

  // Actualizar el tamaño del pincel cuando cambie
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageLoaded) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Actualizar el tamaño del pincel
    ctx.lineWidth = brushSize;
  }, [brushSize, imageLoaded]);

  // Funciones de dibujo
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!imageLoaded || isProcessing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    setIsDrawing(true);
    setHasMask(true);

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    ctx.beginPath();
    ctx.moveTo(
      (e.clientX - rect.left) * scaleX,
      (e.clientY - rect.top) * scaleY
    );
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !imageLoaded || isProcessing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    ctx.lineTo(
      (e.clientX - rect.left) * scaleX,
      (e.clientY - rect.top) * scaleY
    );
    ctx.stroke();
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  // Limpiar la máscara
  const clearMask = () => {
    const canvas = canvasRef.current;
    if (!canvas || !imageUrl) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Recargar la imagen original
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = imageUrl;

    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);
      setHasMask(false);
    };
  };

  // Crear máscara y procesar imagen
  const handleEraseObjects = async () => {
    if (!imageFile || !hasMask) {
      toast({
        title: "❌ Error",
        description: "Debes dibujar una máscara sobre las áreas que deseas borrar",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear máscara desde el canvas
      const canvas = canvasRef.current;
      if (!canvas) throw new Error("Canvas no disponible");

      // Crear canvas temporal para la máscara
      const maskCanvas = document.createElement("canvas");
      maskCanvas.width = canvas.width;
      maskCanvas.height = canvas.height;
      const maskCtx = maskCanvas.getContext("2d");
      if (!maskCtx) throw new Error("No se pudo crear contexto de máscara");

      // Fondo negro (áreas a preservar)
      maskCtx.fillStyle = "black";
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

      // Copiar solo las áreas dibujadas (blancas) del canvas original
      const originalCtx = canvas.getContext("2d");
      if (!originalCtx) throw new Error("No se pudo obtener contexto original");

      // Obtener los datos de imagen del canvas original
      const imageData = originalCtx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Crear nueva imagen de máscara
      const maskImageData = maskCtx.createImageData(canvas.width, canvas.height);
      const maskData = maskImageData.data;

      // Procesar píxel por píxel para extraer solo las áreas dibujadas
      for (let i = 0; i < data.length; i += 4) {
        // Si el píxel es blanco semi-transparente (máscara dibujada)
        if (data[i] > 200 && data[i + 1] > 200 && data[i + 2] > 200 && data[i + 3] > 100) {
          maskData[i] = 255;     // R
          maskData[i + 1] = 255; // G
          maskData[i + 2] = 255; // B
          maskData[i + 3] = 255; // A
        } else {
          maskData[i] = 0;       // R
          maskData[i + 1] = 0;   // G
          maskData[i + 2] = 0;   // B
          maskData[i + 3] = 255; // A
        }
      }

      maskCtx.putImageData(maskImageData, 0, 0);

      // Convertir máscara a blob
      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, "image/png");
      });

      if (!maskBlob) throw new Error("No se pudo crear la máscara");

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('mask', maskBlob, 'mask.png');
      formData.append('output_format', outputFormat);
      formData.append('grow_mask', growMask.toString());

      // Llamar a la API
      const response = await fetch('/api/v1/ai-editor/erase', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success && result.image_url) {
        onResult(result.image_url);
        toast({
          title: "🎉 ¡Objetos borrados!",
          description: "La imagen ha sido procesada exitosamente",
        });
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error erasing objects:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-900">Borrar Objetos</h4>
            <p className="text-sm text-blue-700 mt-1">
              Dibuja sobre los objetos que deseas eliminar de la imagen. Las áreas marcadas serán borradas automáticamente.
            </p>
          </div>
        </div>
      </div>

      {/* Canvas para dibujar la máscara */}
      <div className="space-y-4">
        <h4 className="font-medium">Dibuja sobre los objetos a borrar:</h4>
        <div className="border rounded-lg overflow-hidden bg-gray-50">
          <canvas
            ref={canvasRef}
            onMouseDown={startDrawing}
            onMouseMove={draw}
            onMouseUp={endDrawing}
            onMouseLeave={endDrawing}
            className="max-w-full cursor-crosshair block mx-auto"
            style={{
              maxHeight: "400px",
              width: "100%",
              objectFit: "contain",
              backgroundColor: "#f9fafb"
            }}
          />
        </div>

        {/* Controles del pincel */}
        <div className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Tamaño del pincel:</span>
            <input
              type="range"
              min="5"
              max="50"
              value={brushSize}
              onChange={(e) => setBrushSize(parseInt(e.target.value))}
              className="w-24"
              disabled={isProcessing}
            />
            <span className="text-sm text-gray-600 w-8">{brushSize}px</span>
          </div>

          <Button
            onClick={clearMask}
            variant="outline"
            size="sm"
            disabled={!hasMask || isProcessing}
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Limpiar
          </Button>
        </div>
      </div>

      {/* Configuración avanzada */}
      <div className="space-y-4">
        <h4 className="font-medium">Configuración:</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Formato de salida */}
          <div>
            <label className="block text-sm font-medium mb-2">Formato de salida:</label>
            <select
              value={outputFormat}
              onChange={(e) => setOutputFormat(e.target.value as "png" | "webp" | "jpeg")}
              className="w-full p-2 border border-gray-300 rounded-md"
              disabled={isProcessing}
            >
              <option value="webp">WebP (Recomendado)</option>
              <option value="png">PNG (Transparencia)</option>
              <option value="jpeg">JPEG (Menor tamaño)</option>
            </select>
          </div>

          {/* Expandir máscara */}
          <div>
            <label className="block text-sm font-medium mb-2">
              Expandir máscara: {growMask}px
            </label>
            <input
              type="range"
              min="0"
              max="20"
              value={growMask}
              onChange={(e) => setGrowMask(parseInt(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
            <p className="text-xs text-gray-500 mt-1">
              Expande los bordes de la máscara para suavizar transiciones
            </p>
          </div>
        </div>
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleEraseObjects}
        disabled={!hasMask || isProcessing}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Borrando objetos...
          </>
        ) : (
          <>
            <Eraser className="w-4 h-4 mr-2" />
            Borrar Objetos Marcados
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>3 créditos</strong></p>
      </div>

      {/* Instrucciones */}
      <div className="text-sm text-gray-600 space-y-1">
        <p><strong>Instrucciones:</strong></p>
        <ul className="list-disc list-inside space-y-1 ml-2">
          <li>Dibuja sobre los objetos que deseas eliminar</li>
          <li>Usa un pincel más grande para objetos grandes</li>
          <li>Puedes limpiar y volver a dibujar si es necesario</li>
          <li>La herramienta funciona mejor con objetos bien definidos</li>
        </ul>
      </div>
    </div>
  );
}

// Componente específico para rellenar áreas (inpaint)
interface InpaintAreasToolProps {
  imageFile: File;
  imageUrl: string;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function InpaintAreasTool({ imageFile, imageUrl, onResult, isProcessing, setIsProcessing }: InpaintAreasToolProps) {
  const { toast } = useToast();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("png");
  const [growMask, setGrowMask] = useState(5);
  const [brushSize, setBrushSize] = useState(20);
  const [isDrawing, setIsDrawing] = useState(false);
  const [hasMask, setHasMask] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [prompt, setPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [stylePreset, setStylePreset] = useState<string>("");

  // Cargar la imagen en el canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageUrl) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = imageUrl;

    img.onload = () => {
      // Ajustar el tamaño del canvas
      canvas.width = img.width;
      canvas.height = img.height;

      // Dibujar la imagen original
      ctx.drawImage(img, 0, 0);

      // Configurar el contexto para dibujar la máscara
      ctx.lineJoin = "round";
      ctx.lineCap = "round";
      ctx.strokeStyle = "rgba(0, 255, 0, 0.6)"; // Verde semi-transparente para inpaint
      ctx.lineWidth = brushSize;

      setImageLoaded(true);
    };
  }, [imageUrl]);

  // Actualizar el tamaño del pincel cuando cambie
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || !imageLoaded) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Actualizar el tamaño del pincel
    ctx.lineWidth = brushSize;
  }, [brushSize, imageLoaded]);

  // Funciones de dibujo
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!imageLoaded || isProcessing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    setIsDrawing(true);
    setHasMask(true);

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    ctx.beginPath();
    ctx.moveTo(
      (e.clientX - rect.left) * scaleX,
      (e.clientY - rect.top) * scaleY
    );
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !imageLoaded || isProcessing) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    ctx.lineTo(
      (e.clientX - rect.left) * scaleX,
      (e.clientY - rect.top) * scaleY
    );
    ctx.stroke();
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  // Limpiar la máscara
  const clearMask = () => {
    const canvas = canvasRef.current;
    if (!canvas || !imageUrl) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Recargar la imagen original
    const img = new Image();
    img.crossOrigin = "anonymous";
    img.src = imageUrl;

    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.drawImage(img, 0, 0);
      setHasMask(false);
    };
  };

  // Crear máscara y procesar imagen
  const handleInpaintAreas = async () => {
    if (!imageFile || !hasMask || !prompt.trim()) {
      toast({
        title: "❌ Error",
        description: "Debes dibujar una máscara y escribir un prompt",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear máscara desde el canvas
      const canvas = canvasRef.current;
      if (!canvas) throw new Error("Canvas no disponible");

      // Crear canvas temporal para la máscara
      const maskCanvas = document.createElement("canvas");
      maskCanvas.width = canvas.width;
      maskCanvas.height = canvas.height;
      const maskCtx = maskCanvas.getContext("2d");
      if (!maskCtx) throw new Error("No se pudo crear contexto de máscara");

      // Fondo negro (áreas a preservar)
      maskCtx.fillStyle = "black";
      maskCtx.fillRect(0, 0, maskCanvas.width, maskCanvas.height);

      // Copiar solo las áreas dibujadas (verdes) del canvas original
      const originalCtx = canvas.getContext("2d");
      if (!originalCtx) throw new Error("No se pudo obtener contexto original");

      // Obtener los datos de imagen del canvas original
      const imageData = originalCtx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;

      // Crear nueva imagen de máscara
      const maskImageData = maskCtx.createImageData(canvas.width, canvas.height);
      const maskData = maskImageData.data;

      // Procesar píxel por píxel para extraer solo las áreas dibujadas
      for (let i = 0; i < data.length; i += 4) {
        // Si el píxel es verde semi-transparente (máscara dibujada)
        if (data[i] < 100 && data[i + 1] > 200 && data[i + 2] < 100 && data[i + 3] > 100) {
          maskData[i] = 255;     // R
          maskData[i + 1] = 255; // G
          maskData[i + 2] = 255; // B
          maskData[i + 3] = 255; // A
        } else {
          maskData[i] = 0;       // R
          maskData[i + 1] = 0;   // G
          maskData[i + 2] = 0;   // B
          maskData[i + 3] = 255; // A
        }
      }

      maskCtx.putImageData(maskImageData, 0, 0);

      // Convertir máscara a blob
      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, "image/png");
      });

      if (!maskBlob) throw new Error("No se pudo crear la máscara");

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('mask', maskBlob, 'mask.png');
      formData.append('prompt', prompt);
      if (negativePrompt.trim()) {
        formData.append('negative_prompt', negativePrompt);
      }
      formData.append('output_format', outputFormat);
      formData.append('grow_mask', growMask.toString());
      if (stylePreset) {
        formData.append('style_preset', stylePreset);
      }

      // Llamar a la API
      console.log('🚀 Enviando request de inpaint a:', '/api/v1/ai-editor/inpaint');
      console.log('📦 FormData keys:', Array.from(formData.keys()));

      const response = await fetch('/api/v1/ai-editor/inpaint', {
        method: 'POST',
        body: formData,
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Error response:', errorData);
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();
      console.log('✅ Success response:', result);

      if (result.success && result.image_url) {
        console.log('🖼️ Setting result image:', result.image_url.substring(0, 50) + '...');
        onResult(result.image_url);
        toast({
          title: "🎉 ¡Áreas rellenadas!",
          description: "La imagen ha sido procesada exitosamente",
        });
      } else {
        console.error('❌ No image_url in result:', result);
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error inpainting areas:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-blue-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-blue-900">Rellenar Áreas</h4>
            <p className="text-sm text-blue-700 mt-1">
              Rellena áreas específicas con contenido nuevo basado en tu prompt.
              Dibuja sobre las áreas que quieres rellenar y describe qué contenido deseas generar.
            </p>
          </div>
        </div>
      </div>

      {/* Canvas para dibujar la máscara */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Selecciona las áreas a rellenar</label>
          <div className="border rounded-lg overflow-hidden bg-gray-50">
            <canvas
              ref={canvasRef}
              onMouseDown={startDrawing}
              onMouseMove={draw}
              onMouseUp={endDrawing}
              onMouseLeave={endDrawing}
              className="w-full h-auto max-h-96 cursor-crosshair"
              style={{ display: imageLoaded ? 'block' : 'none' }}
            />
            {!imageLoaded && (
              <div className="h-48 flex items-center justify-center">
                <div className="text-center">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mx-auto mb-2"></div>
                  <p className="text-sm text-muted-foreground">Cargando imagen...</p>
                </div>
              </div>
            )}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Dibuja sobre las áreas que quieres rellenar (aparecerán en verde)
          </p>
        </div>

        {/* Controles del pincel */}
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <label className="text-sm font-medium mb-1 block">Tamaño del pincel: {brushSize}px</label>
            <input
              type="range"
              min="5"
              max="50"
              value={brushSize}
              onChange={(e) => setBrushSize(Number(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
          </div>
          <Button
            variant="outline"
            onClick={clearMask}
            disabled={!hasMask || isProcessing}
            size="sm"
          >
            <RotateCcw className="w-4 h-4 mr-1" />
            Limpiar
          </Button>
        </div>
      </div>

      {/* Configuración de prompts */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">
            Prompt (describe qué quieres generar) *
          </label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Ej: una hermosa flor roja, un gato durmiendo, un paisaje montañoso..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={3}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe específicamente qué contenido quieres generar en las áreas marcadas
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">
            Prompt negativo (opcional)
          </label>
          <textarea
            value={negativePrompt}
            onChange={(e) => setNegativePrompt(e.target.value)}
            placeholder="Ej: borroso, de baja calidad, distorsionado..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={2}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe qué NO quieres que aparezca en el resultado
          </p>
        </div>
      </div>

      {/* Configuración avanzada */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Expansión de máscara: {growMask}px</label>
          <input
            type="range"
            min="0"
            max="100"
            value={growMask}
            onChange={(e) => setGrowMask(Number(e.target.value))}
            className="w-full"
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Expande los bordes de la máscara para mejores transiciones (0-100 píxeles)
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Formato de salida</label>
          <div className="grid grid-cols-3 gap-2">
            {(['png', 'webp', 'jpeg'] as const).map((format) => (
              <Button
                key={format}
                variant={outputFormat === format ? "default" : "outline"}
                onClick={() => setOutputFormat(format)}
                className="text-sm"
                disabled={isProcessing}
              >
                {format.toUpperCase()}
              </Button>
            ))}
          </div>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Estilo (opcional)</label>
          <select
            value={stylePreset}
            onChange={(e) => setStylePreset(e.target.value)}
            className="w-full p-2 border rounded-lg"
            disabled={isProcessing}
          >
            <option value="">Sin estilo específico</option>
            <option value="photographic">Fotográfico</option>
            <option value="digital-art">Arte Digital</option>
            <option value="cinematic">Cinematográfico</option>
            <option value="anime">Anime</option>
            <option value="fantasy-art">Arte Fantástico</option>
            <option value="3d-model">Modelo 3D</option>
            <option value="analog-film">Película Analógica</option>
            <option value="comic-book">Cómic</option>
            <option value="enhance">Mejorado</option>
            <option value="isometric">Isométrico</option>
            <option value="line-art">Arte Lineal</option>
            <option value="low-poly">Low Poly</option>
            <option value="modeling-compound">Compuesto de Modelado</option>
            <option value="neon-punk">Neon Punk</option>
            <option value="origami">Origami</option>
            <option value="pixel-art">Pixel Art</option>
            <option value="tile-texture">Textura de Azulejo</option>
          </select>
          <p className="text-xs text-muted-foreground mt-1">
            Guía el modelo hacia un estilo particular
          </p>
        </div>
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleInpaintAreas}
        disabled={isProcessing || !hasMask || !prompt.trim()}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Rellenando áreas...
          </>
        ) : (
          <>
            <PaintBucket className="w-4 h-4 mr-2" />
            Rellenar Áreas
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>3 créditos</strong></p>
      </div>
    </div>
  );
}

// Componente específico para expandir imagen (outpaint)
interface OutpaintImageToolProps {
  imageFile: File;
  imageUrl: string;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function OutpaintImageTool({ imageFile, imageUrl, onResult, isProcessing, setIsProcessing }: OutpaintImageToolProps) {
  const { toast } = useToast();
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("png");
  const [left, setLeft] = useState(0);
  const [right, setRight] = useState(0);
  const [up, setUp] = useState(0);
  const [down, setDown] = useState(0);
  const [prompt, setPrompt] = useState("");
  const [creativity, setCreativity] = useState(0.5);
  const [stylePreset, setStylePreset] = useState<string>("");

  const handleOutpaintImage = async () => {
    if (!imageFile) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para procesar",
        variant: "destructive",
      });
      return;
    }

    // Validar que al menos una dirección esté especificada
    if (left === 0 && right === 0 && up === 0 && down === 0) {
      toast({
        title: "❌ Error",
        description: "Debes especificar al menos una dirección de expansión",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('left', left.toString());
      formData.append('right', right.toString());
      formData.append('up', up.toString());
      formData.append('down', down.toString());
      formData.append('creativity', creativity.toString());
      formData.append('output_format', outputFormat);

      if (prompt.trim()) {
        formData.append('prompt', prompt);
      }

      if (stylePreset) {
        formData.append('style_preset', stylePreset);
      }

      // Llamar a la API
      console.log('🚀 Enviando request de outpaint a:', '/api/v1/ai-editor/outpaint');
      console.log('📦 FormData keys:', Array.from(formData.keys()));

      const response = await fetch('/api/v1/ai-editor/outpaint', {
        method: 'POST',
        body: formData,
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Error response:', errorData);
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();
      console.log('✅ Success response:', result);

      if (result.success && result.image_url) {
        console.log('🖼️ Setting result image:', result.image_url.substring(0, 50) + '...');
        onResult(result.image_url);
        toast({
          title: "🎉 ¡Imagen expandida!",
          description: "La imagen ha sido expandida exitosamente",
        });
      } else {
        console.error('❌ No image_url in result:', result);
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error expanding image:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-green-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-green-900">Expandir Imagen</h4>
            <p className="text-sm text-green-700 mt-1">
              Extiende tu imagen más allá de sus bordes originales. Especifica cuántos píxeles expandir en cada dirección.
              La IA generará contenido coherente que se integre naturalmente con la imagen original.
            </p>
          </div>
        </div>
      </div>

      {/* Vista previa de la imagen original */}
      <div className="border rounded-lg overflow-hidden">
        <img
          src={imageUrl}
          alt="Imagen original"
          className="w-full h-auto max-h-64 object-contain"
        />
      </div>

      {/* Configuración de direcciones */}
      <div className="space-y-4">
        <h4 className="font-semibold">Direcciones de Expansión (píxeles)</h4>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Izquierda: {left}px</label>
            <input
              type="range"
              min="0"
              max="2000"
              value={left}
              onChange={(e) => setLeft(Number(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Derecha: {right}px</label>
            <input
              type="range"
              min="0"
              max="2000"
              value={right}
              onChange={(e) => setRight(Number(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Arriba: {up}px</label>
            <input
              type="range"
              min="0"
              max="2000"
              value={up}
              onChange={(e) => setUp(Number(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Abajo: {down}px</label>
            <input
              type="range"
              min="0"
              max="2000"
              value={down}
              onChange={(e) => setDown(Number(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
          </div>
        </div>

        <p className="text-xs text-muted-foreground">
          Al menos una dirección debe ser mayor a 0. Máximo 2000 píxeles por dirección.
        </p>
      </div>

      {/* Prompt opcional */}
      <div>
        <label className="text-sm font-medium mb-2 block">Prompt (opcional)</label>
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Describe qué quieres que aparezca en las áreas expandidas (ej: 'paisaje montañoso', 'cielo azul con nubes', 'más océano')"
          className="w-full p-3 border rounded-lg resize-none"
          rows={3}
          disabled={isProcessing}
        />
        <p className="text-xs text-muted-foreground mt-1">
          Describe el contenido que quieres generar en las áreas expandidas
        </p>
      </div>

      {/* Creatividad */}
      <div>
        <label className="text-sm font-medium mb-2 block">Creatividad: {creativity}</label>
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={creativity}
          onChange={(e) => setCreativity(Number(e.target.value))}
          className="w-full"
          disabled={isProcessing}
        />
        <p className="text-xs text-muted-foreground mt-1">
          0.0 = Más conservador, 1.0 = Más creativo
        </p>
      </div>

      {/* Formato de salida */}
      <div>
        <label className="text-sm font-medium mb-2 block">Formato de salida</label>
        <div className="grid grid-cols-3 gap-2">
          {(['png', 'webp', 'jpeg'] as const).map((format) => (
            <Button
              key={format}
              variant={outputFormat === format ? "default" : "outline"}
              onClick={() => setOutputFormat(format)}
              className="text-sm"
              disabled={isProcessing}
            >
              {format.toUpperCase()}
            </Button>
          ))}
        </div>
      </div>

      {/* Estilo */}
      <div>
        <label className="text-sm font-medium mb-2 block">Estilo (opcional)</label>
        <select
          value={stylePreset}
          onChange={(e) => setStylePreset(e.target.value)}
          className="w-full p-2 border rounded-lg"
          disabled={isProcessing}
        >
          <option value="">Sin estilo específico</option>
          <option value="photographic">Fotográfico</option>
          <option value="digital-art">Arte Digital</option>
          <option value="cinematic">Cinematográfico</option>
          <option value="anime">Anime</option>
          <option value="fantasy-art">Arte Fantástico</option>
          <option value="3d-model">Modelo 3D</option>
          <option value="analog-film">Película Analógica</option>
          <option value="comic-book">Cómic</option>
          <option value="enhance">Mejorado</option>
          <option value="isometric">Isométrico</option>
          <option value="line-art">Arte Lineal</option>
          <option value="low-poly">Low Poly</option>
          <option value="modeling-compound">Compuesto de Modelado</option>
          <option value="neon-punk">Neon Punk</option>
          <option value="origami">Origami</option>
          <option value="pixel-art">Pixel Art</option>
          <option value="tile-texture">Textura de Azulejo</option>
        </select>
        <p className="text-xs text-muted-foreground mt-1">
          Guía el modelo hacia un estilo particular
        </p>
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleOutpaintImage}
        disabled={isProcessing || (left === 0 && right === 0 && up === 0 && down === 0)}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Expandiendo imagen...
          </>
        ) : (
          <>
            <Expand className="w-4 h-4 mr-2" />
            Expandir Imagen
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>4 créditos</strong></p>
      </div>
    </div>
  );
}

// Componente específico para reemplazar objetos (search and replace)
interface SearchReplaceToolProps {
  imageFile: File;
  imageUrl: string;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function SearchReplaceTool({ imageFile, imageUrl, onResult, isProcessing, setIsProcessing }: SearchReplaceToolProps) {
  const { toast } = useToast();
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("png");
  const [prompt, setPrompt] = useState("");
  const [searchPrompt, setSearchPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [growMask, setGrowMask] = useState(3);
  const [stylePreset, setStylePreset] = useState<string>("");

  const handleSearchReplace = async () => {
    if (!imageFile) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para procesar",
        variant: "destructive",
      });
      return;
    }

    // Validar prompts requeridos
    if (!prompt.trim()) {
      toast({
        title: "❌ Error",
        description: "El prompt de reemplazo es requerido",
        variant: "destructive",
      });
      return;
    }

    if (!searchPrompt.trim()) {
      toast({
        title: "❌ Error",
        description: "El prompt de búsqueda es requerido",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('prompt', prompt);
      formData.append('search_prompt', searchPrompt);
      formData.append('grow_mask', growMask.toString());
      formData.append('output_format', outputFormat);

      if (negativePrompt.trim()) {
        formData.append('negative_prompt', negativePrompt);
      }

      if (stylePreset) {
        formData.append('style_preset', stylePreset);
      }

      // Llamar a la API
      console.log('🚀 Enviando request de search-replace a:', '/api/v1/ai-editor/search-replace');
      console.log('📦 FormData keys:', Array.from(formData.keys()));

      const response = await fetch('/api/v1/ai-editor/search-replace', {
        method: 'POST',
        body: formData,
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Error response:', errorData);
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();
      console.log('✅ Success response:', result);

      if (result.success && result.image_url) {
        console.log('🖼️ Setting result image:', result.image_url.substring(0, 50) + '...');
        onResult(result.image_url);
        toast({
          title: "🎉 ¡Objeto reemplazado!",
          description: "La imagen ha sido procesada exitosamente",
        });
      } else {
        console.error('❌ No image_url in result:', result);
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error replacing object:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-purple-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-purple-900">Reemplazar Objetos</h4>
            <p className="text-sm text-purple-700 mt-1">
              Busca automáticamente objetos específicos en la imagen y los reemplaza con contenido nuevo.
              No necesitas crear máscaras manualmente - la IA identifica y reemplaza los objetos por ti.
            </p>
          </div>
        </div>
      </div>

      {/* Vista previa de la imagen original */}
      <div className="border rounded-lg overflow-hidden">
        <img
          src={imageUrl}
          alt="Imagen original"
          className="w-full h-auto max-h-64 object-contain"
        />
      </div>

      {/* Configuración de prompts */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">
            ¿Qué objeto quieres buscar? *
          </label>
          <input
            type="text"
            value={searchPrompt}
            onChange={(e) => setSearchPrompt(e.target.value)}
            placeholder="Ej: perro, coche, gafas, persona, árbol..."
            className="w-full p-3 border rounded-lg"
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe de forma simple el objeto que quieres reemplazar
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">
            ¿Con qué lo quieres reemplazar? *
          </label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Ej: un golden retriever sentado en el césped, un coche deportivo rojo, unas gafas de sol elegantes..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={3}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe específicamente cómo quieres que se vea el objeto de reemplazo
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">
            Prompt negativo (opcional)
          </label>
          <textarea
            value={negativePrompt}
            onChange={(e) => setNegativePrompt(e.target.value)}
            placeholder="Ej: borroso, de baja calidad, distorsionado, irreal..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={2}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe qué NO quieres que aparezca en el resultado
          </p>
        </div>
      </div>

      {/* Configuración avanzada */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Expansión de área: {growMask}px</label>
          <input
            type="range"
            min="0"
            max="20"
            value={growMask}
            onChange={(e) => setGrowMask(Number(e.target.value))}
            className="w-full"
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Expande el área de reemplazo para mejores transiciones (0-20 píxeles)
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Formato de salida</label>
          <div className="grid grid-cols-3 gap-2">
            {(['png', 'webp', 'jpeg'] as const).map((format) => (
              <Button
                key={format}
                variant={outputFormat === format ? "default" : "outline"}
                onClick={() => setOutputFormat(format)}
                className="text-sm"
                disabled={isProcessing}
              >
                {format.toUpperCase()}
              </Button>
            ))}
          </div>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Estilo (opcional)</label>
          <select
            value={stylePreset}
            onChange={(e) => setStylePreset(e.target.value)}
            className="w-full p-2 border rounded-lg"
            disabled={isProcessing}
          >
            <option value="">Sin estilo específico</option>
            <option value="photographic">Fotográfico</option>
            <option value="digital-art">Arte Digital</option>
            <option value="cinematic">Cinematográfico</option>
            <option value="anime">Anime</option>
            <option value="fantasy-art">Arte Fantástico</option>
            <option value="3d-model">Modelo 3D</option>
            <option value="analog-film">Película Analógica</option>
            <option value="comic-book">Cómic</option>
            <option value="enhance">Mejorado</option>
            <option value="isometric">Isométrico</option>
            <option value="line-art">Arte Lineal</option>
            <option value="low-poly">Low Poly</option>
            <option value="modeling-compound">Compuesto de Modelado</option>
            <option value="neon-punk">Neon Punk</option>
            <option value="origami">Origami</option>
            <option value="pixel-art">Pixel Art</option>
            <option value="tile-texture">Textura de Azulejo</option>
          </select>
          <p className="text-xs text-muted-foreground mt-1">
            Guía el modelo hacia un estilo particular
          </p>
        </div>
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleSearchReplace}
        disabled={isProcessing || !prompt.trim() || !searchPrompt.trim()}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Reemplazando objeto...
          </>
        ) : (
          <>
            <RefreshCw className="w-4 h-4 mr-2" />
            Reemplazar Objeto
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>4 créditos</strong></p>
      </div>
    </div>
  );
}

// Componente específico para cambiar colores (search and recolor)
interface SearchRecolorToolProps {
  imageFile: File;
  imageUrl: string;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function SearchRecolorTool({ imageFile, imageUrl, onResult, isProcessing, setIsProcessing }: SearchRecolorToolProps) {
  const { toast } = useToast();
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("png");
  const [prompt, setPrompt] = useState("");
  const [selectPrompt, setSelectPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [growMask, setGrowMask] = useState(3);
  const [stylePreset, setStylePreset] = useState<string>("");

  const handleSearchRecolor = async () => {
    if (!imageFile) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para procesar",
        variant: "destructive",
      });
      return;
    }

    // Validar prompts requeridos
    if (!prompt.trim()) {
      toast({
        title: "❌ Error",
        description: "El prompt de colores es requerido",
        variant: "destructive",
      });
      return;
    }

    if (!selectPrompt.trim()) {
      toast({
        title: "❌ Error",
        description: "El prompt de selección es requerido",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('prompt', prompt);
      formData.append('select_prompt', selectPrompt);
      formData.append('grow_mask', growMask.toString());
      formData.append('output_format', outputFormat);

      if (negativePrompt.trim()) {
        formData.append('negative_prompt', negativePrompt);
      }

      if (stylePreset) {
        formData.append('style_preset', stylePreset);
      }

      // Llamar a la API
      console.log('🚀 Enviando request de search-recolor a:', '/api/v1/ai-editor/search-recolor');
      console.log('📦 FormData keys:', Array.from(formData.keys()));

      const response = await fetch('/api/v1/ai-editor/search-recolor', {
        method: 'POST',
        body: formData,
      });

      console.log('📡 Response status:', response.status);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Error response:', errorData);
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();
      console.log('✅ Success response:', result);

      if (result.success && result.image_url) {
        console.log('🖼️ Setting result image:', result.image_url.substring(0, 50) + '...');
        onResult(result.image_url);
        toast({
          title: "🎨 ¡Colores cambiados!",
          description: "La imagen ha sido procesada exitosamente",
        });
      } else {
        console.error('❌ No image_url in result:', result);
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error changing colors:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-orange-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-orange-900">Cambiar Colores</h4>
            <p className="text-sm text-orange-700 mt-1">
              Busca automáticamente objetos específicos en la imagen y cambia sus colores.
              No necesitas crear máscaras manualmente - la IA identifica y recolorea los objetos por ti.
            </p>
          </div>
        </div>
      </div>

      {/* Vista previa de la imagen original */}
      <div className="border rounded-lg overflow-hidden">
        <img
          src={imageUrl}
          alt="Imagen original"
          className="w-full h-auto max-h-64 object-contain"
        />
      </div>

      {/* Configuración de prompts */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">
            ¿Qué objeto quieres recolorear? *
          </label>
          <input
            type="text"
            value={selectPrompt}
            onChange={(e) => setSelectPrompt(e.target.value)}
            placeholder="Ej: coche, camisa, flores, cabello, ojos..."
            className="w-full p-3 border rounded-lg"
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe de forma simple el objeto que quieres cambiar de color
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">
            ¿Qué colores quieres aplicar? *
          </label>
          <textarea
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Ej: coche rojo brillante, camisa azul con rayas blancas, flores moradas y rosadas..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={3}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe específicamente los colores y apariencia que quieres aplicar
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">
            Colores a evitar (opcional)
          </label>
          <textarea
            value={negativePrompt}
            onChange={(e) => setNegativePrompt(e.target.value)}
            placeholder="Ej: colores apagados, tonos grises, colores desaturados..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={2}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe qué colores o efectos NO quieres que aparezcan
          </p>
        </div>
      </div>

      {/* Configuración avanzada */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">Expansión de área: {growMask}px</label>
          <input
            type="range"
            min="0"
            max="20"
            value={growMask}
            onChange={(e) => setGrowMask(Number(e.target.value))}
            className="w-full"
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Expande el área de recoloreo para mejores transiciones (0-20 píxeles)
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Formato de salida</label>
          <div className="grid grid-cols-3 gap-2">
            {(['png', 'webp', 'jpeg'] as const).map((format) => (
              <Button
                key={format}
                variant={outputFormat === format ? "default" : "outline"}
                onClick={() => setOutputFormat(format)}
                className="text-sm"
                disabled={isProcessing}
              >
                {format.toUpperCase()}
              </Button>
            ))}
          </div>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Estilo (opcional)</label>
          <select
            value={stylePreset}
            onChange={(e) => setStylePreset(e.target.value)}
            className="w-full p-2 border rounded-lg"
            disabled={isProcessing}
          >
            <option value="">Sin estilo específico</option>
            <option value="photographic">Fotográfico</option>
            <option value="digital-art">Arte Digital</option>
            <option value="cinematic">Cinematográfico</option>
            <option value="anime">Anime</option>
            <option value="fantasy-art">Arte Fantástico</option>
            <option value="3d-model">Modelo 3D</option>
            <option value="analog-film">Película Analógica</option>
            <option value="comic-book">Cómic</option>
            <option value="enhance">Mejorado</option>
            <option value="isometric">Isométrico</option>
            <option value="line-art">Arte Lineal</option>
            <option value="low-poly">Low Poly</option>
            <option value="modeling-compound">Compuesto de Modelado</option>
            <option value="neon-punk">Neon Punk</option>
            <option value="origami">Origami</option>
            <option value="pixel-art">Pixel Art</option>
            <option value="tile-texture">Textura de Azulejo</option>
          </select>
          <p className="text-xs text-muted-foreground mt-1">
            Guía el modelo hacia un estilo particular
          </p>
        </div>
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleSearchRecolor}
        disabled={isProcessing || !prompt.trim() || !selectPrompt.trim()}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Cambiando colores...
          </>
        ) : (
          <>
            <Palette className="w-4 h-4 mr-2" />
            Cambiar Colores
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>5 créditos</strong></p>
      </div>
    </div>
  );
}

// Componente específico para reemplazar fondo y reajustar iluminación
interface ReplaceBackgroundToolProps {
  imageFile: File;
  imageUrl: string;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

function ReplaceBackgroundTool({ imageFile, imageUrl, onResult, isProcessing, setIsProcessing }: ReplaceBackgroundToolProps) {
  const { toast } = useToast();
  const [outputFormat, setOutputFormat] = useState<"png" | "webp" | "jpeg">("png");
  const [backgroundPrompt, setBackgroundPrompt] = useState("");
  const [foregroundPrompt, setForegroundPrompt] = useState("");
  const [negativePrompt, setNegativePrompt] = useState("");
  const [preserveOriginalSubject, setPreserveOriginalSubject] = useState(0.6);
  const [lightSourceDirection, setLightSourceDirection] = useState<"none" | "above" | "below" | "left" | "right">("none");
  const [lightSourceStrength, setLightSourceStrength] = useState(0.3);
  const [progress, setProgress] = useState(0);

  const handleReplaceBackground = async () => {
    if (!imageFile) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para procesar",
        variant: "destructive",
      });
      return;
    }

    // Validar prompt requerido
    if (!backgroundPrompt.trim()) {
      toast({
        title: "❌ Error",
        description: "El prompt de fondo es requerido",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);
      setProgress(0);

      // Preparar opciones para el servicio
      const options: ReplaceBackgroundOptions = {
        subjectImage: imageFile,
        backgroundPrompt: backgroundPrompt.trim(),
        foregroundPrompt: foregroundPrompt.trim() || undefined,
        negativePrompt: negativePrompt.trim() || undefined,
        preserveOriginalSubject,
        lightSourceDirection: lightSourceDirection !== "none" ? lightSourceDirection : undefined,
        lightSourceStrength: lightSourceDirection !== "none" ? lightSourceStrength : undefined,
        outputFormat,
      };

      console.log('🚀 Iniciando replace background con opciones:', options);

      // Función para actualizar progreso
      const updateProgress = (progressValue: number, message?: string) => {
        setProgress(progressValue);
        console.log(`📊 Progreso: ${progressValue}% - ${message || ''}`);
      };

      // Usar el servicio existente con polling
      const resultUrl = await replaceBackgroundWithPolling(
        options,
        updateProgress,
        40, // 40 intentos (aproximadamente 6-7 minutos)
        10000 // 10 segundos entre consultas
      );

      console.log('✅ Replace background completado:', resultUrl.substring(0, 50) + '...');

      onResult(resultUrl);
      setProgress(100);

      toast({
        title: "🎨 ¡Fondo reemplazado!",
        description: "La imagen ha sido procesada exitosamente",
      });

    } catch (error) {
      console.error('Error replacing background:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-teal-50 border border-teal-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-teal-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-teal-900">Reemplazar Fondo</h4>
            <p className="text-sm text-teal-700 mt-1">
              Reemplaza automáticamente el fondo de la imagen con nuevos escenarios e iluminación.
              Perfecto para fotografía de productos, retratos y contenido creativo.
            </p>
          </div>
        </div>
      </div>

      {/* Vista previa de la imagen original */}
      <div className="border rounded-lg overflow-hidden">
        <img
          src={imageUrl}
          alt="Imagen original"
          className="w-full h-auto max-h-64 object-contain"
        />
      </div>

      {/* Configuración de prompts */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">
            Descripción del nuevo fondo *
          </label>
          <textarea
            value={backgroundPrompt}
            onChange={(e) => setBackgroundPrompt(e.target.value)}
            placeholder="Ej: playa tropical al atardecer, oficina moderna minimalista, bosque encantado con luces mágicas..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={3}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe específicamente el fondo que quieres generar. Puedes escribir en español - se traduce automáticamente.
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">
            Descripción del sujeto (opcional)
          </label>
          <input
            type="text"
            value={foregroundPrompt}
            onChange={(e) => setForegroundPrompt(e.target.value)}
            placeholder="Ej: persona con camisa azul, producto electrónico, mascota..."
            className="w-full p-3 border rounded-lg"
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Ayuda a evitar que el fondo afecte al sujeto principal. Puedes escribir en español.
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">
            Elementos a evitar (opcional)
          </label>
          <textarea
            value={negativePrompt}
            onChange={(e) => setNegativePrompt(e.target.value)}
            placeholder="Ej: colores desaturados, iluminación artificial, elementos distorsionados..."
            className="w-full p-3 border rounded-lg resize-none"
            rows={2}
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Describe qué NO quieres que aparezca en el resultado. Puedes escribir en español.
          </p>
        </div>
      </div>

      {/* Configuración avanzada */}
      <div className="space-y-4">
        <div>
          <label className="text-sm font-medium mb-2 block">
            Preservar sujeto original: {Math.round(preserveOriginalSubject * 100)}%
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={preserveOriginalSubject}
            onChange={(e) => setPreserveOriginalSubject(Number(e.target.value))}
            className="w-full"
            disabled={isProcessing}
          />
          <p className="text-xs text-muted-foreground mt-1">
            Controla cuánto del sujeto original se mantiene (100% = exacto, 0% = nueva iluminación)
          </p>
        </div>

        <div>
          <label className="text-sm font-medium mb-2 block">Dirección de la luz</label>
          <select
            value={lightSourceDirection}
            onChange={(e) => setLightSourceDirection(e.target.value as any)}
            className="w-full p-2 border rounded-lg"
            disabled={isProcessing}
          >
            <option value="none">Sin dirección específica</option>
            <option value="above">Desde arriba</option>
            <option value="below">Desde abajo</option>
            <option value="left">Desde la izquierda</option>
            <option value="right">Desde la derecha</option>
          </select>
          <p className="text-xs text-muted-foreground mt-1">
            Controla la dirección de la fuente de luz principal
          </p>
        </div>

        {lightSourceDirection !== "none" && (
          <div>
            <label className="text-sm font-medium mb-2 block">
              Intensidad de la luz: {Math.round(lightSourceStrength * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={lightSourceStrength}
              onChange={(e) => setLightSourceStrength(Number(e.target.value))}
              className="w-full"
              disabled={isProcessing}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Controla la intensidad de la fuente de luz (100% = más brillante, 0% = más tenue)
            </p>
          </div>
        )}

        <div>
          <label className="text-sm font-medium mb-2 block">Formato de salida</label>
          <div className="grid grid-cols-3 gap-2">
            {(['png', 'webp', 'jpeg'] as const).map((format) => (
              <Button
                key={format}
                variant={outputFormat === format ? "default" : "outline"}
                onClick={() => setOutputFormat(format)}
                className="text-sm"
                disabled={isProcessing}
              >
                {format.toUpperCase()}
              </Button>
            ))}
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            PNG mantiene transparencia, WebP es eficiente, JPEG es compatible
          </p>
        </div>
      </div>

      {/* Barra de progreso */}
      {isProcessing && progress > 0 && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Procesando imagen...</span>
            <span>{progress}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-teal-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <p className="text-xs text-muted-foreground text-center">
            Este proceso puede tardar varios minutos. Puedes navegar a otras secciones mientras se procesa.
          </p>
        </div>
      )}

      {/* Botón de procesamiento */}
      <Button
        onClick={handleReplaceBackground}
        disabled={isProcessing || !backgroundPrompt.trim()}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            Reemplazando fondo...
          </>
        ) : (
          <>
            <ImageIcon className="w-4 h-4 mr-2" />
            Reemplazar Fondo
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>💳 Esta operación consume <strong>8 créditos</strong></p>
      </div>
    </div>
  );
}

// Componente específico para mejorar calidad de imagen
interface UpscaleImageToolProps {
  imageFile: File;
  onResult: (result: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

/**
 * Traduce prompts del español al inglés para Stability AI
 * Stability AI solo acepta prompts en inglés
 */
function translatePromptToEnglish(spanishPrompt: string): string {
  if (!spanishPrompt || spanishPrompt.trim() === '') return spanishPrompt;

  // Diccionario completo de traducciones para prompts de IA
  const translations: Record<string, string> = {
    // Personas y partes del cuerpo
    'persona': 'person',
    'personas': 'people',
    'hombre': 'man',
    'mujer': 'woman',
    'niño': 'child',
    'niña': 'girl',
    'bebé': 'baby',
    'retrato': 'portrait',
    'cara': 'face',
    'rostro': 'face',
    'sonrisa': 'smile',
    'ojos': 'eyes',
    'cabello': 'hair',
    'pelo': 'hair',
    'manos': 'hands',
    'mano': 'hand',
    'brazos': 'arms',
    'brazo': 'arm',
    'piernas': 'legs',
    'pierna': 'leg',
    'pies': 'feet',
    'pie': 'foot',
    'cabeza': 'head',
    'cuerpo': 'body',

    // Lugares y ambientes
    'playa': 'beach',
    'montaña': 'mountain',
    'montañas': 'mountains',
    'bosque': 'forest',
    'ciudad': 'city',
    'campo': 'countryside',
    'jardín': 'garden',
    'parque': 'park',
    'casa': 'house',
    'oficina': 'office',
    'estudio': 'studio',
    'sala': 'living room',
    'cocina': 'kitchen',
    'dormitorio': 'bedroom',
    'baño': 'bathroom',
    'paisaje': 'landscape',
    'naturaleza': 'nature',
    'cielo': 'sky',
    'mar': 'sea',
    'océano': 'ocean',
    'río': 'river',
    'lago': 'lake',
    'calle': 'street',
    'carretera': 'road',

    // Colores
    'rojo': 'red',
    'azul': 'blue',
    'verde': 'green',
    'amarillo': 'yellow',
    'negro': 'black',
    'blanco': 'white',
    'gris': 'gray',
    'rosa': 'pink',
    'morado': 'purple',
    'violeta': 'violet',
    'naranja': 'orange',
    'marrón': 'brown',
    'dorado': 'golden',
    'plateado': 'silver',

    // Ropa y accesorios
    'ropa': 'clothes',
    'camisa': 'shirt',
    'camiseta': 't-shirt',
    'pantalones': 'pants',
    'pantalón': 'pants',
    'vestido': 'dress',
    'falda': 'skirt',
    'zapatos': 'shoes',
    'zapato': 'shoe',
    'sombrero': 'hat',
    'gafas': 'glasses',
    'bolsillos': 'pockets',
    'bolsillo': 'pocket',
    'chaqueta': 'jacket',
    'abrigo': 'coat',

    // Objetos comunes
    'producto': 'product',
    'comercial': 'commercial',
    'profesional': 'professional',
    'fotografía': 'photography',
    'imagen': 'image',
    'foto': 'photo',
    'animal': 'animal',
    'animales': 'animals',
    'perro': 'dog',
    'gato': 'cat',
    'coche': 'car',
    'auto': 'car',
    'automóvil': 'car',
    'edificio': 'building',
    'comida': 'food',
    'bebida': 'drink',
    'mesa': 'table',
    'silla': 'chair',
    'ventana': 'window',
    'puerta': 'door',

    // Descriptivos
    'hermoso': 'beautiful',
    'hermosa': 'beautiful',
    'bonito': 'pretty',
    'bonita': 'pretty',
    'elegante': 'elegant',
    'moderno': 'modern',
    'moderna': 'modern',
    'antiguo': 'old',
    'antigua': 'old',
    'nuevo': 'new',
    'nueva': 'new',
    'grande': 'big',
    'pequeño': 'small',
    'pequeña': 'small',
    'alto': 'tall',
    'alta': 'tall',
    'bajito': 'short',
    'baja': 'short',
    'limpio': 'clean',
    'limpia': 'clean',
    'claro': 'clear',
    'clara': 'clear',
    'oscuro': 'dark',
    'oscura': 'dark',
    'brillante': 'bright',
    'suave': 'soft',
    'duro': 'hard',
    'dura': 'hard',
    'joven': 'young',
    'viejo': 'old',
    'vieja': 'old',

    // Preposiciones y conectores
    'con': 'with',
    'sin': 'without',
    'en': 'in',
    'dentro': 'inside',
    'fuera': 'outside',
    'sobre': 'on',
    'bajo': 'under',
    'entre': 'between',
    'cerca': 'near',
    'lejos': 'far',
    'de': 'of',
    'del': 'of the',
    'desde': 'from',
    'hacia': 'towards',
    'para': 'for',
    'por': 'by',

    // Artículos y determinantes
    'la': 'the',
    'el': 'the',
    'los': 'the',
    'las': 'the',
    'un': 'a',
    'una': 'a',
    'unos': 'some',
    'unas': 'some',
    'este': 'this',
    'esta': 'this',
    'estos': 'these',
    'estas': 'these',
    'ese': 'that',
    'esa': 'that',
    'esos': 'those',
    'esas': 'those',

    // Verbos comunes (conjugaciones básicas)
    'es': 'is',
    'está': 'is',
    'están': 'are',
    'son': 'are',
    'tiene': 'has',
    'tienen': 'have',
    'lleva': 'wears',
    'llevan': 'wear',
    'mira': 'looks',
    'miran': 'look',
    'camina': 'walks',
    'caminan': 'walk',
    'corre': 'runs',
    'corren': 'run',
    'sonríe': 'smiles',
    'sonríen': 'smile',

    // Conectores lógicos
    'y': 'and',
    'o': 'or',
    'pero': 'but',
    'también': 'also',
    'además': 'also',
    'porque': 'because',
    'cuando': 'when',
    'donde': 'where',
    'como': 'like',
    'muy': 'very',
    'más': 'more',
    'menos': 'less',
    'mucho': 'much',
    'poco': 'little',
    'todo': 'all',
    'nada': 'nothing',
    'algo': 'something',
  };

  // Primero, manejar frases completas comunes
  const phraseTranslations: Record<string, string> = {
    'retrato de una persona': 'portrait of a person',
    'retrato de un hombre': 'portrait of a man',
    'retrato de una mujer': 'portrait of a woman',
    'paisaje natural': 'natural landscape',
    'producto comercial': 'commercial product',
    'fotografía profesional': 'professional photography',
    'imagen de alta calidad': 'high quality image',
    'foto de estudio': 'studio photo',
    'iluminación profesional': 'professional lighting',
    'fondo blanco': 'white background',
    'fondo negro': 'black background',
    'las manos en los bolsillos': 'hands in pockets',
    'con las manos en los bolsillos': 'with hands in pockets',
    'vestido de blanco y negro': 'dressed in black and white',
    'ropa blanca y negra': 'black and white clothes',
    'está vestido de': 'is dressed in',
    'está vestida de': 'is dressed in',
    'lleva puesto': 'is wearing',
    'tiene puesto': 'is wearing',
  };

  let translatedPrompt = spanishPrompt.toLowerCase();

  // Aplicar traducciones de frases completas primero
  Object.entries(phraseTranslations).forEach(([spanish, english]) => {
    const regex = new RegExp(spanish.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    translatedPrompt = translatedPrompt.replace(regex, english);
  });

  // Luego aplicar traducciones palabra por palabra
  Object.entries(translations).forEach(([spanish, english]) => {
    const regex = new RegExp(`\\b${spanish.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b`, 'gi');
    translatedPrompt = translatedPrompt.replace(regex, english);
  });

  // Limpiar espacios múltiples y capitalizar primera letra
  translatedPrompt = translatedPrompt
    .replace(/\s+/g, ' ')
    .trim()
    .replace(/^./, char => char.toUpperCase());

  console.log(`🌐 Prompt traducido: "${spanishPrompt}" → "${translatedPrompt}"`);
  return translatedPrompt;
}

function UpscaleImageTool({ imageFile, onResult, isProcessing, setIsProcessing }: UpscaleImageToolProps) {
  const { toast } = useToast();
  const [upscaleType, setUpscaleType] = useState("conservative");
  const [creativity, setCreativity] = useState(0.35); // Default para conservative según documentación
  const [prompt, setPrompt] = useState("");
  const [stylePreset, setStylePreset] = useState("");

  const handleUpscaleImage = async () => {
    if (!imageFile) {
      toast({
        title: "❌ Error",
        description: "No hay imagen para procesar",
        variant: "destructive",
      });
      return;
    }

    // Validaciones específicas por tipo
    if ((upscaleType === "conservative" || upscaleType === "creative") && !prompt.trim()) {
      toast({
        title: "❌ Error",
        description: `El prompt es requerido para el modo ${upscaleType}`,
        variant: "destructive",
      });
      return;
    }

    try {
      setIsProcessing(true);

      // Crear FormData
      const formData = new FormData();
      formData.append('image', imageFile);
      formData.append('upscale_type', upscaleType);
      formData.append('output_format', 'webp');

      if (upscaleType !== "fast") {
        // Traducir el prompt al inglés para Stability AI
        const originalPrompt = prompt.trim() || 'high quality image';
        console.log(`🔄 Traduciendo prompt: "${originalPrompt}"`);
        const translatedPrompt = translatePromptToEnglish(originalPrompt);
        console.log(`✅ Prompt traducido: "${translatedPrompt}"`);

        formData.append('prompt', translatedPrompt);
      }

      formData.append('creativity', creativity.toString());

      if (upscaleType === "creative" && stylePreset) {
        formData.append('style_preset', stylePreset);
      }

      // Llamar a la API
      const response = await fetch('/api/v1/images/enhance-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Error al procesar la imagen');
      }

      const result = await response.json();

      if (result.success) {
        if (upscaleType === "creative" && result.metadata?.generation_id) {
          // Para creative upscale, iniciar polling
          await pollCreativeUpscaleResult(result.metadata.generation_id);
        } else if (result.url) {
          // Para fast y conservative, resultado inmediato
          onResult(result.url);
          toast({
            title: "🎉 ¡Calidad mejorada!",
            description: `Imagen procesada exitosamente con modo ${upscaleType}`,
          });
        }
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Error enhancing image:', error);
      toast({
        title: "❌ Error",
        description: error instanceof Error ? error.message : "Error al procesar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const pollCreativeUpscaleResult = async (generationId: string) => {
    const maxAttempts = 30; // 5 minutos máximo
    const pollInterval = 10000; // 10 segundos

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        await new Promise(resolve => setTimeout(resolve, pollInterval));

        const response = await fetch(`/api/v1/images/enhance-image/creative/result/${generationId}`);
        const result = await response.json();

        if (result.success && result.url) {
          onResult(result.url);
          toast({
            title: "🎉 ¡Calidad mejorada!",
            description: "Imagen procesada exitosamente con modo creativo",
          });
          return;
        } else if (result.metadata?.status === "processing") {
          // Continuar polling
          toast({
            title: "⏳ Procesando...",
            description: `Intento ${attempt + 1}/${maxAttempts} - Modo creativo en progreso`,
          });
          continue;
        } else {
          throw new Error(result.error || 'Error en el procesamiento creativo');
        }
      } catch (error) {
        console.error('Error polling creative upscale:', error);
        if (attempt === maxAttempts - 1) {
          toast({
            title: "❌ Timeout",
            description: "El procesamiento creativo tomó demasiado tiempo",
            variant: "destructive",
          });
        }
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Información de la herramienta */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Info className="h-5 w-5 text-purple-500 mt-0.5" />
          <div>
            <h4 className="font-semibold text-purple-900">Mejorar Calidad</h4>
            <p className="text-sm text-purple-700 mt-1">
              Esta herramienta mejora la calidad y resolución de la imagen usando IA avanzada.
              Ideal para aumentar la nitidez y los detalles de fotografías.
            </p>
          </div>
        </div>
      </div>

      {/* Configuración */}
      <div className="space-y-4">
        {/* Selector de tipo de upscale */}
        <div>
          <label className="text-sm font-medium mb-2 block">
            Tipo de Mejora
          </label>
          <select
            value={upscaleType}
            onChange={(e) => {
              setUpscaleType(e.target.value);
              // Ajustar creatividad según el tipo y documentación oficial
              if (e.target.value === "creative") {
                setCreativity(0.3); // Rango 0.1-0.5
              } else if (e.target.value === "conservative") {
                setCreativity(0.35); // Rango 0.2-0.5 (default: 0.35)
              }
            }}
            className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isProcessing}
          >
            <option value="fast">🚀 Rápido (1 seg, 4x, 1 crédito)</option>
            <option value="conservative">🎯 Conservador (hasta 4K, preserva aspectos, 25 créditos)</option>
            <option value="creative">🎨 Creativo (hasta 4K, reimagina imagen, 25 créditos)</option>
          </select>
          <div className="text-xs text-muted-foreground mt-1">
            {upscaleType === "fast" && "Mejora rápida sin prompt requerido"}
            {upscaleType === "conservative" && "Preserva la imagen original, requiere descripción"}
            {upscaleType === "creative" && "Reimagina y mejora la imagen, requiere descripción"}
          </div>
        </div>

        {/* Creatividad */}
        {upscaleType !== "fast" && (
          <div>
            <label className="text-sm font-medium mb-2 block">
              Creatividad: {creativity.toFixed(1)}
            </label>
            <input
              type="range"
              min={upscaleType === "creative" ? "0.1" : "0.2"}
              max="0.5"
              step="0.05"
              value={creativity}
              onChange={(e) => setCreativity(parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              disabled={isProcessing}
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Conservador ({upscaleType === "creative" ? "0.1" : "0.2"})</span>
              <span>Creativo (0.5)</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Valores más altos permiten más cambios creativos en la imagen
            </p>
          </div>
        )}

        {/* Descripción */}
        {upscaleType !== "fast" && (
          <div>
            <label className="text-sm font-medium mb-2 block">
              Descripción {upscaleType === "creative" || upscaleType === "conservative" ? "(requerida)" : "(opcional)"}
            </label>
            <textarea
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="Describe la imagen para mejores resultados (ej: retrato de una persona, paisaje natural, producto comercial)"
              className="w-full p-3 border border-gray-300 rounded-lg resize-none"
              rows={3}
              disabled={isProcessing}
              required={upscaleType === "creative" || upscaleType === "conservative"}
            />
            <p className="text-xs text-muted-foreground mt-1">
              Una descripción ayuda a la IA a entender mejor el contenido de la imagen
            </p>
          </div>
        )}

        {/* Style Preset para Creative */}
        {upscaleType === "creative" && (
          <div>
            <label className="text-sm font-medium mb-2 block">
              Estilo (opcional)
            </label>
            <select
              value={stylePreset}
              onChange={(e) => setStylePreset(e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={isProcessing}
            >
              <option value="">Sin estilo específico</option>
              <option value="photographic">📸 Fotográfico</option>
              <option value="digital-art">🎨 Arte Digital</option>
              <option value="cinematic">🎬 Cinematográfico</option>
              <option value="anime">🌸 Anime</option>
              <option value="fantasy-art">🧙 Arte Fantástico</option>
              <option value="enhance">✨ Mejorar</option>
              <option value="3d-model">🎯 Modelo 3D</option>
              <option value="analog-film">📽️ Película Analógica</option>
            </select>
          </div>
        )}
      </div>

      {/* Botón de procesamiento */}
      <Button
        onClick={handleUpscaleImage}
        disabled={isProcessing || !imageFile}
        className="w-full"
        size="lg"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2"></div>
            {upscaleType === "creative" ? "Procesando (puede tomar varios minutos)..." : "Mejorando calidad..."}
          </>
        ) : (
          <>
            <Sparkles className="w-4 h-4 mr-2" />
            Mejorar Calidad ({upscaleType === "fast" ? "Rápido" : upscaleType === "conservative" ? "Conservador" : "Creativo"})
          </>
        )}
      </Button>

      {/* Información de créditos */}
      <div className="text-center text-sm text-muted-foreground">
        <p>
          💳 Esta operación consume{" "}
          <strong>
            {upscaleType === "fast" ? "1 crédito" : "25 créditos"}
          </strong>
        </p>
        {upscaleType === "creative" && (
          <p className="text-xs mt-1">⏱️ El modo creativo puede tomar varios minutos</p>
        )}
      </div>
    </div>
  );
}