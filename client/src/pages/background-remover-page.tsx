/**
 * Herramienta para eliminar fondos de imágenes usando Stability AI
 * Sigue el mismo patrón que poster-creator, meme-creator y ad-creator
 */
import React, { useCallback, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  removeBackground,
  removeBackgroundWithProgress,
} from "@/services/background-remover-service";
import { useBackgroundTasks } from "@/context/BackgroundTasksContext";
import {
  Download,
  Wand2,
  ImagePlus,
  <PERSON>rkles,
  CheckCircle,
  Eraser,
  Upload,
  Loader2,
  Heart,
  <PERSON>py,
  Zap,
  Trash2,
  <PERSON><PERSON>s,
  Eye,
  EyeOff,
} from "lucide-react";

// Tipos para el estado de la aplicación
interface GeneratedBackgroundRemoval {
  id: string;
  originalUrl: string;
  processedUrl: string;
  originalFilename: string;
  outputFormat: string;
  timestamp: number;
}

interface SavedBackgroundRemoval {
  id: string;
  originalUrl: string;
  processedUrl: string;
  originalFilename: string;
  outputFormat: string;
  timestamp: number;
}

// Constantes
const SAVED_BACKGROUND_REMOVALS_KEY = 'emma_saved_background_removals';

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

export default function BackgroundRemoverPage() {
  const { toast } = useToast();
  const { addTask, updateTask } = useBackgroundTasks();

  // Estados principales
  const [currentBackgroundRemoval, setCurrentBackgroundRemoval] = useState<GeneratedBackgroundRemoval | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Estados para imagen
  const [originalImage, setOriginalImage] = useState<File | null>(null);
  const [originalImagePreview, setOriginalImagePreview] = useState<string | null>(null);
  const [originalName, setOriginalName] = useState<string>("");

  // Estados para favoritos
  const [savedBackgroundRemovals, setSavedBackgroundRemovals] = useLocalStorage<SavedBackgroundRemoval[]>(SAVED_BACKGROUND_REMOVALS_KEY, []);
  const [currentBackgroundRemovalSaved, setCurrentBackgroundRemovalSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  // Función para validar archivos de imagen
  const validateImageFile = (file: File) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return { valid: false, error: 'Tipo de archivo no válido. Use JPEG, PNG o WebP.' };
    }

    if (file.size > maxSize) {
      return { valid: false, error: 'El archivo es muy grande. Máximo 10MB.' };
    }

    return { valid: true };
  };

  // Función para manejar la imagen
  const handleImageChange = (file: File) => {
    const validation = validateImageFile(file);
    if (!validation.valid) {
      toast({
        title: "Archivo inválido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setOriginalImage(file);
    setOriginalName(file.name);
    const reader = new FileReader();
    reader.onload = (e) => {
      setOriginalImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setCurrentBackgroundRemoval(null);
    setCurrentBackgroundRemovalSaved(false);
  };

  // Función para procesar imagen
  const handleProcessImage = async () => {
    if (!originalImage) {
      toast({
        title: "Imagen requerida",
        description: "Debes subir una imagen para eliminar el fondo.",
        variant: "destructive",
      });
      return;
    }

    setIsProcessing(true);
    setCurrentBackgroundRemoval(null);
    setCurrentBackgroundRemovalSaved(false);

    try {
      const taskId = `background_removal_${Date.now()}`;

      const imageUrl = await removeBackgroundWithProgress(
        originalImage,
        (progress, message) => {
          updateTask(taskId, {
            progress,
            message: message || `Progreso: ${progress}%`,
          });
        }
      );

      const newBackgroundRemoval: GeneratedBackgroundRemoval = {
        id: Date.now().toString(),
        originalUrl: originalImagePreview!,
        processedUrl: imageUrl,
        originalFilename: originalName,
        outputFormat: "webp",
        timestamp: Date.now(),
      };

      setCurrentBackgroundRemoval(newBackgroundRemoval);

      toast({
        title: "✅ ¡Éxito!",
        description: "Fondo eliminado exitosamente.",
      });
    } catch (error) {
      console.error("Error processing image:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al eliminar el fondo",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Función para crear un objeto SavedBackgroundRemoval
  const createSavedBackgroundRemoval = (backgroundRemovalData: GeneratedBackgroundRemoval): SavedBackgroundRemoval => {
    return {
      id: backgroundRemovalData.id,
      originalUrl: backgroundRemovalData.originalUrl,
      processedUrl: backgroundRemovalData.processedUrl,
      originalFilename: backgroundRemovalData.originalFilename,
      outputFormat: backgroundRemovalData.outputFormat,
      timestamp: backgroundRemovalData.timestamp,
    };
  };

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(async () => {
    if (!currentBackgroundRemoval) return;

    try {
      if (currentBackgroundRemovalSaved) {
        // Remover de favoritos
        const updatedBackgroundRemovals = savedBackgroundRemovals.filter(
          (saved) => saved.processedUrl !== currentBackgroundRemoval.processedUrl
        );
        setSavedBackgroundRemovals(updatedBackgroundRemovals);
        setCurrentBackgroundRemovalSaved(false);

        toast({
          title: "💔 Removido de favoritos",
          description: "Imagen removida de tus favoritos.",
        });
      } else {
        // Agregar a favoritos
        const backgroundRemovalData = createSavedBackgroundRemoval(currentBackgroundRemoval);

        const newBackgroundRemoval = createSavedBackgroundRemoval(backgroundRemovalData);
        const updatedBackgroundRemovals = [newBackgroundRemoval, ...savedBackgroundRemovals].slice(0, 50); // Limitar a 50

        setSavedBackgroundRemovals(updatedBackgroundRemovals);
        setCurrentBackgroundRemovalSaved(true);

        toast({
          title: "❤️ ¡Guardado en favoritos!",
          description: "Imagen guardada exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar la imagen. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [currentBackgroundRemoval, currentBackgroundRemovalSaved, savedBackgroundRemovals, setSavedBackgroundRemovals, toast]);

  // Función para descargar imagen
  const handleDownload = async () => {
    if (!currentBackgroundRemoval?.processedUrl) return;

    try {
      const response = await fetch(currentBackgroundRemoval.processedUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `no-bg-${Date.now()}.${currentBackgroundRemoval.outputFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Descarga iniciada",
        description: "La imagen se está descargando",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "No se pudo descargar la imagen",
        variant: "destructive",
      });
    }
  };

  // Función para copiar imagen al portapapeles
  const handleCopyToClipboard = async () => {
    if (!currentBackgroundRemoval?.processedUrl) return;

    try {
      const response = await fetch(currentBackgroundRemoval.processedUrl);
      const blob = await response.blob();

      await navigator.clipboard.write([
        new ClipboardItem({
          [blob.type]: blob
        })
      ]);

      toast({
        title: "✅ ¡Copiado!",
        description: "Imagen copiada al portapapeles exitosamente.",
      });
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast({
        title: "❌ Error",
        description: "No se pudo copiar la imagen al portapapeles.",
        variant: "destructive",
      });
    }
  };

  // Función para limpiar imagen
  const handleClearImage = () => {
    setOriginalImage(null);
    setOriginalImagePreview(null);
    setOriginalName("");
    setCurrentBackgroundRemoval(null);
    setCurrentBackgroundRemovalSaved(false);
  };

  // Verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (currentBackgroundRemoval) {
      const isAlreadySaved = savedBackgroundRemovals.some(
        (saved) => saved.processedUrl === currentBackgroundRemoval.processedUrl
      );
      setCurrentBackgroundRemovalSaved(isAlreadySaved);
    }
  }, [currentBackgroundRemoval, savedBackgroundRemovals]);

  return (
    <DashboardLayout pageTitle="Eliminador de Fondos">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Eraser className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Eliminador de Fondos
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Elimina fondos de imágenes con precisión profesional usando IA avanzada.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Sparkles className="w-3 h-3 mr-1" />
                  Precisión profesional
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Zap className="w-3 h-3 mr-1" />
                  Procesamiento rápido
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Wand2 className="w-3 h-3 mr-1" />
                  IA avanzada
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Contenido principal */}
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Panel de Control */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="lg:col-span-1"
            >
              <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Settings className="h-5 w-5 text-purple-600" />
                    Panel de Control
                  </CardTitle>
                  <CardDescription>
                    Sube tu imagen y elimina el fondo automáticamente
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Subida de imagen */}
                  <div className="space-y-3">
                    <Label className="text-sm font-medium">📁 Subir Imagen</Label>
                    {originalImagePreview ? (
                      <div className="relative border-2 border-gray-300 rounded-lg overflow-hidden">
                        <img
                          src={originalImagePreview}
                          alt="Imagen original"
                          className="w-full h-48 object-cover bg-white"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0"
                          onClick={handleClearImage}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ) : (
                      <div
                        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
                        onClick={() => document.getElementById("image-upload")?.click()}
                      >
                        <Upload className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                        <p className="text-sm text-gray-600 mb-1">
                          Haz clic para subir imagen
                        </p>
                        <p className="text-xs text-gray-500">
                          JPG, PNG, WebP (máx. 10MB)
                        </p>
                        <input
                          id="image-upload"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageChange(file);
                          }}
                        />
                      </div>
                    )}
                  </div>

                  {/* Botón de procesamiento */}
                  <div className="space-y-3">
                    <Button
                      onClick={handleProcessImage}
                      disabled={isProcessing || !originalImage}
                      className="w-full h-12 text-lg bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                      size="lg"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                          Eliminando fondo...
                        </>
                      ) : (
                        <>
                          <Eraser className="w-5 h-5 mr-2" />
                          Eliminar Fondo
                        </>
                      )}
                    </Button>

                    {originalImage && (
                      <Button
                        onClick={handleClearImage}
                        variant="outline"
                        className="w-full"
                        size="sm"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Limpiar Imagen
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Área de Visualización */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="latest" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Última Generación
                  </TabsTrigger>
                  <TabsTrigger value="saved" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    Guardados ({savedBackgroundRemovals.length})
                  </TabsTrigger>
                </TabsList>

                {/* Tab: Última Generación */}
                <TabsContent value="latest" className="mt-0">
                  <AnimatePresence mode="wait">
                    {currentBackgroundRemoval ? (
                      <motion.div
                        key="result"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        transition={{ duration: 0.3 }}
                      >
                        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                          <CardHeader className="pb-4">
                            <CardTitle className="flex items-center gap-2">
                              <Sparkles className="w-5 h-5 text-purple-600" />
                              Resultado sin Fondo
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-6">
                            {/* Imagen resultado */}
                            <div className="relative group">
                              <div className="bg-checkered rounded-lg p-4">
                                <img
                                  src={currentBackgroundRemoval.processedUrl}
                                  alt="Imagen sin fondo"
                                  className="w-full rounded-lg shadow-lg"
                                />
                              </div>
                            </div>

                            {/* Imagen original */}
                            <div className="p-4 bg-gray-50 rounded-lg">
                              <h4 className="font-medium text-sm text-gray-700 mb-2">Imagen Original</h4>
                              <img
                                src={currentBackgroundRemoval.originalUrl}
                                alt="Imagen original"
                                className="w-full h-24 object-cover rounded-md"
                              />
                              <p className="text-xs text-gray-500 mt-1">{currentBackgroundRemoval.originalFilename}</p>
                            </div>

                            {/* Información del procesamiento */}
                            <div className="grid grid-cols-2 gap-4 p-4 bg-purple-50 rounded-lg">
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Formato</p>
                                <p className="font-medium text-sm uppercase">{currentBackgroundRemoval.outputFormat}</p>
                              </div>
                              <div className="text-center">
                                <p className="text-xs text-gray-600">Procesado</p>
                                <p className="font-medium text-sm">
                                  {new Date(currentBackgroundRemoval.timestamp).toLocaleDateString()}
                                </p>
                              </div>
                            </div>

                            {/* Botones de acción */}
                            <div className="flex flex-wrap gap-3">
                              <Button
                                onClick={handleDownload}
                                className="flex-1 min-w-[120px]"
                                variant="outline"
                              >
                                <Download className="w-4 h-4 mr-2" />
                                Descargar
                              </Button>
                              <Button
                                onClick={handleCopyToClipboard}
                                className="flex-1 min-w-[120px]"
                                variant="outline"
                              >
                                <Copy className="w-4 h-4 mr-2" />
                                Copiar
                              </Button>
                              <Button
                                onClick={handleToggleFavorite}
                                className={`flex-1 min-w-[120px] ${
                                  currentBackgroundRemovalSaved
                                    ? "bg-red-500 hover:bg-red-600 text-white"
                                    : "bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                                }`}
                              >
                                <Heart className={`w-4 h-4 mr-2 ${currentBackgroundRemovalSaved ? "fill-current" : ""}`} />
                                {currentBackgroundRemovalSaved ? "Quitar" : "Guardar"}
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ) : (
                      <motion.div
                        key="placeholder"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        className="text-center py-16"
                      >
                        <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                          <CardContent className="py-16">
                            <div className="space-y-4">
                              <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto">
                                <Eraser className="w-12 h-12 text-purple-600" />
                              </div>
                              <h3 className="text-xl font-semibold text-gray-700">
                                Listo para eliminar fondos
                              </h3>
                              <p className="text-gray-500 max-w-md mx-auto">
                                Sube una imagen para eliminar automáticamente su fondo con IA.
                              </p>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </TabsContent>

                {/* Tab: Guardados */}
                <TabsContent value="saved" className="mt-0">
                  {savedBackgroundRemovals.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {savedBackgroundRemovals.map((savedBackgroundRemoval) => (
                        <motion.div
                          key={savedBackgroundRemoval.id}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.3 }}
                        >
                          <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-shadow">
                            <CardContent className="p-4">
                              <div className="space-y-4">
                                {/* Imagen resultado */}
                                <div className="relative group">
                                  <div className="bg-checkered rounded-lg p-2">
                                    <img
                                      src={savedBackgroundRemoval.processedUrl}
                                      alt="Imagen sin fondo guardada"
                                      className="w-full h-48 object-cover rounded-lg"
                                    />
                                  </div>
                                </div>

                                {/* Información */}
                                <div className="space-y-2">
                                  <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                      <p className="text-sm font-medium text-gray-700">
                                        {savedBackgroundRemoval.originalFilename}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {new Date(savedBackgroundRemoval.timestamp).toLocaleDateString()}
                                      </p>
                                    </div>
                                  </div>

                                  {/* Configuración */}
                                  <div className="grid grid-cols-1 gap-2 text-xs">
                                    <div className="text-center p-2 bg-gray-50 rounded">
                                      <p className="text-gray-600">Formato</p>
                                      <p className="font-medium uppercase">{savedBackgroundRemoval.outputFormat}</p>
                                    </div>
                                  </div>
                                </div>

                                {/* Botones de acción */}
                                <div className="flex gap-2">
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      // Cargar la imagen en la vista principal
                                      setCurrentBackgroundRemoval({
                                        id: savedBackgroundRemoval.id,
                                        originalUrl: savedBackgroundRemoval.originalUrl,
                                        processedUrl: savedBackgroundRemoval.processedUrl,
                                        originalFilename: savedBackgroundRemoval.originalFilename,
                                        outputFormat: savedBackgroundRemoval.outputFormat,
                                        timestamp: savedBackgroundRemoval.timestamp,
                                      });

                                      // Cambiar a la pestaña "Última Generación"
                                      setMainTab("latest");

                                      toast({
                                        title: "🖼️ Imagen cargada",
                                        description: "Imagen cargada en la vista principal.",
                                      });
                                    }}
                                    className="flex-1"
                                  >
                                    <Eye className="w-3 h-3 mr-1" />
                                    Ver
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={async () => {
                                      try {
                                        const response = await fetch(savedBackgroundRemoval.processedUrl);
                                        const blob = await response.blob();
                                        const url = window.URL.createObjectURL(blob);
                                        const a = document.createElement("a");
                                        a.href = url;
                                        a.download = `no-bg-${savedBackgroundRemoval.id}.${savedBackgroundRemoval.outputFormat}`;
                                        document.body.appendChild(a);
                                        a.click();
                                        window.URL.revokeObjectURL(url);
                                        document.body.removeChild(a);

                                        toast({
                                          title: "Descarga iniciada",
                                          description: "La imagen se está descargando",
                                        });
                                      } catch (error) {
                                        toast({
                                          title: "Error",
                                          description: "No se pudo descargar la imagen",
                                          variant: "destructive",
                                        });
                                      }
                                    }}
                                    className="flex-1"
                                  >
                                    <Download className="w-3 h-3 mr-1" />
                                    Descargar
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={() => {
                                      const updatedBackgroundRemovals = savedBackgroundRemovals.filter(
                                        (saved) => saved.id !== savedBackgroundRemoval.id
                                      );
                                      setSavedBackgroundRemovals(updatedBackgroundRemovals);

                                      toast({
                                        title: "🗑️ Eliminado",
                                        description: "Imagen eliminada de guardados.",
                                      });
                                    }}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="w-3 h-3" />
                                  </Button>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </motion.div>
                      ))}
                    </div>
                  ) : (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-center py-16"
                    >
                      <Card className="shadow-xl border-0 bg-white/90 backdrop-blur-sm">
                        <CardContent className="py-16">
                          <div className="space-y-4">
                            <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mx-auto">
                              <Heart className="w-12 h-12 text-purple-600" />
                            </div>
                            <h3 className="text-xl font-semibold text-gray-700">
                              No hay imágenes guardadas
                            </h3>
                            <p className="text-gray-500 max-w-md mx-auto">
                              Las imágenes que guardes aparecerán aquí para acceso rápido.
                            </p>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )}
                </TabsContent>

              </Tabs>
            </motion.div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
